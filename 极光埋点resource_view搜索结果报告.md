# 极光埋点resource_view搜索结果报告

## 概述
本报告详细记录了对事件ID为`resource_view`的极光埋点的全面搜索结果。

## 搜索范围和方法

### 1. 搜索关键词
- `resource_view`
- `resourceView`
- `RESOURCE_VIEW`
- `ResourceView`
- `EVENT_RESOURCE_VIEW`
- `EVENT.*RESOURCE.*VIEW`
- `EVENT.*VIEW.*RESOURCE`

### 2. 搜索范围
- **文件类型**: 所有Java (.java) 和 Kotlin (.kt) 文件
- **搜索位置**: 整个项目目录
- **重点关注**: 
  - JGTrackManager中的事件常量定义
  - ReportEventName注解的Bean类
  - 实际的极光埋点调用

### 3. 搜索命令
```bash
# 基础搜索
find . -type f \( -name "*.java" -o -name "*.kt" \) -exec grep -l "resource_view" {} \;

# 变体搜索
find . -type f \( -name "*.java" -o -name "*.kt" \) -exec grep -l "resourceView\|RESOURCE_VIEW\|ResourceView" {} \;

# 极光埋点调用搜索
find . -type f \( -name "*.java" -o -name "*.kt" \) -exec grep -l "JGTrackManager.*resource_view\|eventTrack.*resource_view\|EVENT.*RESOURCE_VIEW" {} \;

# Bean类注解搜索
find . -type f \( -name "*.java" -o -name "*.kt" \) -exec grep -l "@ReportEventName.*resource_view\|@ReportName.*resource_view" {} \;

# 事件常量搜索
find . -type f \( -name "*.java" -o -name "*.kt" \) -exec grep -l "EVENT.*RESOURCE.*VIEW\|EVENT.*VIEW.*RESOURCE" {} \;

# 字符串字面量搜索
grep -r "\"resource_view\"" . --include="*.java" --include="*.kt"
```

## 搜索结果

### 1. 找到的文件
搜索到以下文件包含`resource_view`相关内容：
- `app/src/main/java/com/ybmmarket20/home/<USER>/adapter/ConcatAtmosphereHeadPictureAdapter.kt`
- `app/src/main/java/com/ybmmarket20/home/<USER>/adapter/ConcatCapsuleAdvertisementAdapter.kt`
- `app/src/main/java/com/ybmmarket20/home/<USER>/adapter/ConcatPorcelainTilesAdapter.kt`
- `app/src/main/java/com/ybmmarket20/home/<USER>/adapter/ConcatFastEntryAdapter.kt`
- `app/src/main/java/com/ybmmarket20/home/<USER>/adapter/HomeFeedStreamAdapter.kt`
- `app/src/main/java/com/ybmmarket20/home/<USER>/adapter/ConcatFrequentPurchaseListAdapter.kt`
- `app/src/main/java/com/ybmmarket20/home/<USER>/adapter/ConcatSuperValueListAdapter.kt`

### 2. 内容分析
经过详细分析，这些文件中的`resource_view`相关内容都是：

#### 2.1 常量定义
```kotlin
private const val TRACK_RESOURCE_VIEW_KEY = "track_resource_view_key"
```

#### 2.2 Map变量
```kotlin
private val resourceViewTrackMap = hashMapOf<String, Long>()
```

#### 2.3 时间戳记录
```kotlin
resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY]?.let {
    if (System.currentTimeMillis() - it > TRACK_DURATION) {
        resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
    }
} ?: kotlin.run {
    resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
}
```

### 3. 重要发现
**这些都不是实际的极光埋点事件调用**，而是：
- 用于防重复上报的时间戳记录机制
- 本地缓存的Key常量定义
- 业务逻辑中的防抖处理

## 极光埋点系统分析

### 1. JGTrackManager中的事件常量
通过分析`JGTrackManager.kt`文件，找到的与"view"相关的极光埋点事件：
- `EVENT_PRODUCT_VIEW = "product_view"` - 浏览商品详情页
- `EVENT_PRODUCT_VIEW_CLOSE = "product_view_close"` - 退出商品详情页
- `EVENT_SHOP_VIEW = "shop_view"` - 进入店铺

**没有找到`resource_view`事件**。

### 2. 与"resource"相关的极光埋点事件
找到的与"resource"相关的极光埋点事件：
- `EVENT_RESOURCE_CLICK = "resource_click"` - 资源位点击（多个页面使用）

**没有找到`resource_view`事件**。

### 3. ReportEventName注解的Bean类
检查了所有使用`@ReportEventName`或`@ReportName`注解的Bean类：
- `app/src/main/java/com/ybmmarket20/bean/OrderReportBean.kt`
- `app/src/main/java/com/ybmmarket20/bean/product_detail/ProductDetailModel.kt`
- `app/src/main/java/com/ybmmarket20/bean/homesteady/HomeSteadyModel.kt`
- `app/src/main/java/com/ybmmarket20/report/coupon/CouponModel.kt`
- `app/src/main/java/com/ybmmarket20/reportBean/SearchReportBean.kt`
- `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/CommonReportEventBean.kt`

**没有找到任何`resource_view`相关的Bean类**。

## 结论

### 1. 搜索结果
经过全面、详细的搜索，**项目中不存在事件ID为`resource_view`的极光埋点**。

### 2. 可能的情况
1. **事件不存在**: 项目中确实没有这个埋点事件
2. **命名不同**: 可能使用了其他名称，如`resource_exposure`、`resource_show`等
3. **已被移除**: 可能在之前的版本中已经被移除
4. **动态生成**: 可能是通过字符串拼接等方式动态生成的事件名

### 3. 相关事件
虽然没有找到`resource_view`，但找到了相关的事件：
- `resource_click` - 资源位点击事件（存在于多个页面）
- `product_view` - 商品查看事件
- `shop_view` - 店铺查看事件

## 建议

### 1. 确认需求
建议与需求方确认：
- 是否确实存在`resource_view`事件
- 是否是其他类似名称的事件
- 是否需要搜索其他相关的查看类事件

### 2. 扩展搜索
如果需要，可以搜索其他可能的资源查看相关事件：
- `resource_exposure`
- `resource_show`
- `resource_display`
- `page_resource_view`
- `component_view`

### 3. 系统事件确认
根据需求"系统事件不需要去"，需要确认什么是系统事件：
- Activity/Fragment生命周期事件
- 系统级别的页面切换事件
- 应用进入后台/前台的事件

## 技术细节

### 1. 搜索覆盖范围
- ✅ 所有Java和Kotlin源文件
- ✅ 事件常量定义
- ✅ Bean类注解
- ✅ 实际埋点调用
- ✅ 字符串字面量

### 2. 搜索准确性
- ✅ 使用了多种搜索模式
- ✅ 包含了大小写变体
- ✅ 使用了正则表达式匹配
- ✅ 进行了手动验证

### 3. 结果可信度
基于全面的搜索方法和多重验证，可以**高度确信**项目中不存在`resource_view`极光埋点事件。

## 后续行动

1. **与需求方确认**: 确认是否存在此事件或是否为其他名称
2. **扩展搜索**: 如需要，搜索其他可能的相关事件
3. **文档更新**: 更新埋点文档，明确当前存在的事件列表
4. **流程优化**: 建立更好的埋点事件管理和搜索流程
