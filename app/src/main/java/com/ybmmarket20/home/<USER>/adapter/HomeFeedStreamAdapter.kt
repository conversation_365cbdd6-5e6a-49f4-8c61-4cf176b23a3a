package com.ybmmarket20.home.newpage.adapter

import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.os.CountDownTimer
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.util.SparseArray
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.chad.library.adapter.base.BaseViewHolder
import com.xyy.canary.utils.LogUtil
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.TagBean
import com.ybmmarket20.bean.getSingleStepSpannableForGoodsList
import com.ybmmarket20.bean.homesteady.ActionListProductClick
import com.ybmmarket20.bean.homesteady.ComponentBean
import com.ybmmarket20.bean.homesteady.FeedComponentBean
import com.ybmmarket20.bean.homesteady.PageListProductExposure
import com.ybmmarket20.bean.isStep
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.TrackManager
import com.ybmmarket20.common.dp
import com.ybmmarket20.common.glideLoadWithPlaceHolder
import com.ybmmarket20.common.jgTrackResourceProductClick
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.databinding.ItemFeedStreamAdBannerBinding
import com.ybmmarket20.databinding.ItemFeedStreamAdBinding
import com.ybmmarket20.databinding.ItemFeedStreamCommodityBinding
import com.ybmmarket20.home.newpage.bean.HomeFeedStreamBean
import com.ybmmarket20.utils.UiUtils
import com.ybmmarketkotlin.utils.TextWithPrefixTag
import com.ydmmarket.report.ReportManager
import com.youth.banner.indicator.CircleIndicator
import kotlin.random.Random

/**
 * @class   ConcatFeedSteamAdapter
 * <AUTHOR>
 * @date  2024/4/15
 * @description  首页商品Feed流（广告+商品）
 */
class HomeFeedStreamAdapter : HomeComponentStreamAnalysisAdapter<RecyclerView.ViewHolder>() {

    var mDataList: MutableList<HomeFeedStreamBean> = mutableListOf()
        set(value) {
            field.clear()
            field.addAll(value)
            notifyDataSetChanged()
        }

    var mOnItemClickListener: ((position: Int, url: String, clickType: Int, product: RowsBean?, componentBean: FeedComponentBean?) -> Unit)? =
        null

    var mAdRefreshCount: Int = 0 //记录广告位下标 每次刷新要变 这个是共用的 所以一直加一  随机的时候就不变

    var pageId = 0
    var componentBean: FeedComponentBean? = null

    //倒计时
    private val countDownTimerMap: SparseArray<CountDownTimer> = SparseArray()

    private val resourceViewTrackMap = hashMapOf<String, Long>()
    var navigation = ""

    companion object {

        private const val TAG = "HomeFeedStreamAdapter"

        const val AD_BANNER_VIEW_TYPE = 1001 //广告Banner
        const val AD_VIEW_TYPE = 1002 //广告
        const val COMMODITY_VIEW_TYPE = 1003 //商品

        private const val TRACK_RESOURCE_VIEW_KEY = "track_resource_view_key"
        private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
    }

    override fun getDataList(): MutableList<HomeFeedStreamBean> = mDataList

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        mContext = parent.context
        return when (viewType) {
            AD_VIEW_TYPE -> {
                val binding = ItemFeedStreamAdBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ADFeedStreamVH(binding, mOnItemClickListener, this, navigation, componentBean)
            }

            COMMODITY_VIEW_TYPE -> {
                val binding = ItemFeedStreamCommodityBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                CommodityFeedStreamVH(
                    binding,
                    pageId,
                    countDownTimerMap,
                    mOnItemClickListener,
                    navigation,
                    componentBean
                )
            }

            else -> {
                val binding = ItemFeedStreamAdBannerBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ADBannerFeedStreamVH(
                    binding,
                    pageId,
                    mOnItemClickListener,
                    navigation,
                    componentBean
                )
            }
        }
    }


    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int
    ) {
        super.onBindViewHolder(holder, position)
        when (holder) {
            is ADBannerFeedStreamVH -> {
                val data = mDataList[position].bannerList
                data?.let { holder.bind(it) }
            }

            is ADFeedStreamVH -> {
                val data = mDataList[position].advertisement
                data?.let { holder.bind(it) }
            }

            is CommodityFeedStreamVH -> {
                val data = mDataList[position].product
                data?.let { holder.bind(it) }
            }
        }

        resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY]?.let {
            if (System.currentTimeMillis() - it > TRACK_DURATION) {
                resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
            }
        } ?: kotlin.run {
            resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
        }
    }

    fun addDataList(list: MutableList<HomeFeedStreamBean>) {
        if (list.isEmpty()) return
        val preSize = mDataList.size
        mDataList.addAll(list)
        notifyItemRangeInserted(preSize, list.size)
    }

    //
    inner class ADBannerFeedStreamVH(
        val mBinding: ItemFeedStreamAdBannerBinding,
        val pageId: Int,
        val clickCallBack: ((position: Int, url: String, clickType: Int, product: RowsBean?, componentBean: FeedComponentBean?) -> Unit)? = null,
        val navigation: String,
        val componentBean: FeedComponentBean?
    ) : BaseViewHolder(mBinding.root) {

        fun bind(data: MutableList<HomeFeedStreamBean.HomeFeedBanner>) {
            mBinding.apply {
                banner.setAdapter(FeedStreamBannerAdapter(data).apply {
                    pageId = <EMAIL>
                    bannerComponent = componentBean
                    navigation = navigation
                    onItemClickListener = { position: Int, url: String ->
                        clickCallBack?.invoke(position, url, AD_BANNER_VIEW_TYPE, null, null)
                        onBannerItemClick(data[position].trackData)
                    }
                }, true).setIndicator(CircleIndicator(mBinding.root.context)).setLoopTime(3000L)
            }
        }
    }

    inner class CommodityFeedStreamVH(
        val mBinding: ItemFeedStreamCommodityBinding,
        val pageId: Int,
        val countDownTimerMap: SparseArray<CountDownTimer>,
        val clickCallBack: ((position: Int, url: String, clickType: Int, product: RowsBean?, componentBean: FeedComponentBean?) -> Unit)? = null,
        val navigation: String,
        val componentBean: FeedComponentBean?
    ) : BaseViewHolder(mBinding.root) {

        private val resourceViewTrackMap = hashMapOf<String, Long>()

        fun bind(data: RowsBean) {
            mBinding.apply {
                tvTitle.TextWithPrefixTag(data.tags?.titleTags, data.productName)
                tvTitle.setLineSpacing(0f, 1.1f)

                root.context.glideLoadWithPlaceHolder(
                    AppNetConfig.LORD_IMAGE + data.imageUrl,
                    ivCommodity
                )

                tvCompany.text = data.shopName ?: ""

                tvExpirationDate.text = "有效期:${data.nearEffect}"

                when (data.status) {
                    2 -> { //售罄
                        tvShopNoLimit.text = "售罄"
                        tvShopNoLimit.isVisible = true
                    }

                    4 -> { // 已下架
                        tvShopNoLimit.text = "已下架"
                        tvShopNoLimit.isVisible = true
                    }

                    else -> {
                        tvShopNoLimit.text = ""

                        tvShopNoLimit.isVisible = false
                    }
                }


                if (data.markerUrl != null && data.markerUrl.startsWith("http")) {
                    mBinding.root.context.glideLoadWithPlaceHolder(
                        data.markerUrl,
                        ivMarker,
                        R.drawable.transparent,
                        R.drawable.transparent
                    )
                    ivMarker.isVisible = true
                } else {
                    if (TextUtils.isEmpty(data.markerUrl)) {
                        ivMarker.isVisible = false
                    } else {
                        mBinding.root.context.glideLoadWithPlaceHolder(
                            AppNetConfig.LORD_TAG + data.markerUrl,
                            ivMarker,
                            R.drawable.transparent,
                            R.drawable.transparent
                        )
                        ivMarker.isVisible = true
                    }
                }


                val tagList = getAllTags(data)
                tagView.isVisible = tagList.isNotEmpty()
                tagView.bindData(tagList, maxTagCount = 100)

                val showPriceStr = SpannableStringBuilder()
                when (data.showPriceType()) {
                    // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
                    // 首页FEED商品流 文字 “签署协议后可见” 替换为商品价格；
                    0, 2 -> {
                        clCantSeePrice.isVisible = false
                        clNormalPrice.isVisible = false
                        groupFlashSale.isVisible = false
                        clFreeShippingPrice.isVisible = false
                        clTeamworkPrice.isVisible = false
                        showPriceStr.append("¥")

                        // 拼团中的商品，取拼团价/ 秒杀的商品取秒杀价
                        if (data.actPt != null) { //拼团
                            if (data.actPt.stepPriceStatus == 1) {
                                // 多阶梯
                                if (!TextUtils.isEmpty(data.actPt.minSkuPrice)) {
                                    showPriceStr.append(UiUtils.transform(data.actPt.minSkuPrice))
                                    showPriceStr.setSpan(
                                        ForegroundColorSpan(Color.parseColor("#FF2121")),
                                        1,
                                        showPriceStr.length,
                                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                                    )
                                    showPriceStr.setSpan(
                                        AbsoluteSizeSpan(16, true),
                                        1,
                                        showPriceStr.length - 2,
                                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                                    )
                                    showPriceStr.setSpan(
                                        AbsoluteSizeSpan(11, true),
                                        showPriceStr.length - 2,
                                        showPriceStr.length,
                                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                                    )
                                }
                                val showPriceEndStr = SpannableStringBuilder("起")
                                showPriceEndStr.setSpan(
                                        AbsoluteSizeSpan(11, true),
                                        0,
                                        1,
                                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                                    )
                                showPriceStr.append(showPriceEndStr)
//                                if (!TextUtils.isEmpty(data.actPt.maxSkuPrice)) {
//                                    // val showPriceMaxStr = SpannableStringBuilder("-" + data.actPt.maxSkuPrice)
//                                    val showPriceMaxStr = SpannableStringBuilder("-")
//                                    showPriceMaxStr.append(UiUtils.transform(data.actPt.maxSkuPrice))
//                                    showPriceMaxStr.setSpan(
//                                        AbsoluteSizeSpan(16, true),
//                                        1,
//                                        showPriceMaxStr.length - 2,
//                                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
//                                    )
//                                    showPriceMaxStr.setSpan(
//                                        AbsoluteSizeSpan(11, true),
//                                        showPriceMaxStr.length - 2,
//                                        showPriceMaxStr.length,
//                                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
//                                    )
//                                    showPriceStr.append(showPriceMaxStr)
//                                }
                            } else {
                                showPriceStr.append(UiUtils.transform(data.actPt.assemblePrice))
                                showPriceStr.setSpan(
                                    AbsoluteSizeSpan(16, true),
                                    1,
                                    showPriceStr.length - 2,
                                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                                )
                                showPriceStr.setSpan(
                                    AbsoluteSizeSpan(11, true),
                                    showPriceStr.length - 2,
                                    showPriceStr.length,
                                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                                )
                            }
                            tvTeamworkPrice.text = showPriceStr
                            tvTeamworkPriceDiscount.isVisible = !data.showPriceAfterDiscount.isNullOrEmpty()
                            tvTeamworkPriceDiscount.text = data.showPriceAfterDiscount
                            clTeamworkPrice.isVisible = true
                        } else if (data.actSk != null && data.actSk.status == 1) { //秒杀
                            groupFlashSale.isVisible = true

                            processCountDown(this, data, countDownTimerMap)
                            showPriceStr.append(UiUtils.transform(data.actSk.skPrice))
                            showPriceStr.setSpan(
                                AbsoluteSizeSpan(16, true),
                                1,
                                showPriceStr.length - 2,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                            showPriceStr.setSpan(
                                AbsoluteSizeSpan(11, true),
                                showPriceStr.length - 2,
                                showPriceStr.length,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                            tvFlashSalePrice.text = showPriceStr
                            tvFlashSalePriceDiscount.isVisible = !data.showPriceAfterDiscount.isNullOrEmpty()
                            tvFlashSalePriceDiscount.text = data.showPriceAfterDiscount
                        } else if (data.actPgby != null && data.actPgby.assemblePrice != null) { //包邮
                            showPriceStr.append(
                                UiUtils.transform(
                                    data.actPgby.assemblePrice
                                        ?: 0.00
                                )
                            )
                            showPriceStr.setSpan(
                                AbsoluteSizeSpan(16, true),
                                1,
                                showPriceStr.length - 2,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                            showPriceStr.setSpan(
                                AbsoluteSizeSpan(11, true),
                                showPriceStr.length - 2,
                                showPriceStr.length,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                            tvFreeShippingPrice.text = showPriceStr
                            clFreeShippingPrice.isVisible = true
                            tvFreeShippingPriceDiscount.isVisible = !data.showPriceAfterDiscount.isNullOrEmpty()
                            tvFreeShippingPriceDiscount.text = data.showPriceAfterDiscount
                        } else { //普通
                            if (data.rangePriceBean.isStep()) {
                                //阶梯价
                                tvNormalPrice.text =
                                    data.rangePriceBean.getSingleStepSpannableForGoodsList()
                            } else {
                                showPriceStr.append(UiUtils.transform(data.fob))
                                showPriceStr.setSpan(
                                    AbsoluteSizeSpan(16, true),
                                    1,
                                    showPriceStr.length - 2,
                                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                                )
                                showPriceStr.setSpan(
                                    AbsoluteSizeSpan(11, true),
                                    showPriceStr.length - 2,
                                    showPriceStr.length,
                                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                                )
                                tvNormalPrice.text = showPriceStr
                            }
                            clNormalPrice.isVisible = true

                            tvNormalPriceDiscount.isVisible = !data.showPriceAfterDiscount.isNullOrEmpty()
                            tvNormalPriceDiscount.text = data.showPriceAfterDiscount
                        }
                    }
                    // -1 为是否有控销文案
                    -1 -> {
                        showPriceStr.append(data.controlTitle)
                        showPriceStr.setSpan(
                            ForegroundColorSpan(UiUtils.getColor(R.color.color_ff982c)),
                            0,
                            showPriceStr.length,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                        tvCantSeePrice.text = showPriceStr
                        clCantSeePrice.isVisible = true
                        clNormalPrice.isVisible = false
                        groupFlashSale.isVisible = false
                        clFreeShippingPrice.isVisible = false
                        clTeamworkPrice.isVisible = false
                    }
                }


                root.setOnClickListener {
                    val url =
                        "ybmpage://productdetail?${IntentCanst.PRODUCTID}=${data.id}&nsid=${data.nsid ?: ""}&sdata=${data.sdata ?: ""}&sourceType=${data.sourceType}"
                    clickCallBack?.invoke(
                        absoluteAdapterPosition,
                        url,
                        COMMODITY_VIEW_TYPE,
                        data,
                        componentBean
                    )
                    jgTrackProductClick(data)
                    reportActionListProductClick(data, componentBean)
                    onGoodsItemClick(data.trackData, data)
                }

                val map = HashMap<String, Any>().apply {
                    put(TrackManager.FIELD_PAGE_ID, pageId)
                    put(
                        TrackManager.FIELD_OFFSET,
                        <EMAIL> + 1
                    )
                    put(TrackManager.FIELD_TITLE, data.productName ?: "")
                }
                TrackManager.exposureEventTrack(TrackManager.TrackHome.EVENT_SKU_FEED_EXPOSURE, map)
                // 移除极光埋点 - page_list_product_exposure (保留搜索页面)
                // reportPageListProductExposure(data, componentBean)
            }

            if (data.productId.isNullOrEmpty()) {
                jgTrackProductView(data)
                // 移除极光埋点 - page_list_product_exposure (保留搜索页面)
                // reportPageListProductExposure(data, componentBean)
            } else {
                resourceViewTrackMap[data.productId]?.let {
                    if (System.currentTimeMillis() - it > TRACK_DURATION) {
                        jgTrackProductView(data)
                        // 移除极光埋点 - page_list_product_exposure (保留搜索页面)
                        // reportPageListProductExposure(data, componentBean)
                        resourceViewTrackMap[data.productId] = System.currentTimeMillis()
                    }
                } ?: kotlin.run {
                    jgTrackProductView(data)
                    // 移除极光埋点 - page_list_product_exposure (保留搜索页面)
                    // reportPageListProductExposure(data, componentBean)
                    resourceViewTrackMap[data.productId] = System.currentTimeMillis()
                }
            }
        }

        private fun reportActionListProductClick(
            data: RowsBean,
            componentBean: FeedComponentBean?
        ) {
            ReportManager.getInstance().report(ActionListProductClick().apply {
                url = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL
                title = JGTrackManager.TrackHomePage.TITLE
                referrer = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL
                jgspid = componentBean?.jgspid
                page_type = componentBean?.pageType
                page_id = componentBean?.pageId
                page_name = componentBean?.pageName
                page_no = componentBean?.pageNum?.toInt()
                page_size = componentBean?.pageSize?.toInt()
                component_position = componentBean?.componentPosition //组件序号
                component_name = componentBean?.componentName //组件名称
                component_title = componentBean?.componentTitle //组件标题
                sub_module_tab = "" //子模块tab
                sub_module_left_navigation = "" //子模块左侧导航
                sub_module_top_navigation = "" //子模块顶部导航
                product_id = data.productId.toLong() //商品ID
                product_name = data.productName //商品名称
                product_first = data.pId //商品一级分类ID
                product_price = data.productPrice.toDouble() //商品现价
                product_type = data.productName //商品类型
                product_shop_code = data.shopCode //商品店铺编码
                product_shop_name = data.shopName //商品店铺名称
            })
        }

        private fun jgTrackProductView(data: RowsBean) {
            var productTag = ""

            data.tags?.productTags?.let { tagList ->
                tagList.forEachIndexed { index, tagBean ->
                    if (index != tagList.size - 1) {
                        productTag += tagBean.text + "，"
                    } else {
                        productTag += tagBean.text
                    }
                }
            }
            data.tags?.dataTags?.let { tagList ->
                tagList.forEachIndexed { index, tagBean ->
                    if (index != tagList.size - 1) {
                        productTag += tagBean.text + "，"
                    } else {
                        productTag += tagBean.text
                    }
                }
            }
        }

        // 移除极光埋点方法 - page_list_product_exposure (保留搜索页面)
        /*
        private fun reportPageListProductExposure(
            data: RowsBean,
            componentBean: FeedComponentBean?
        ) {
            ReportManager.getInstance().report(PageListProductExposure().apply {
                url = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL
                title = JGTrackManager.TrackHomePage.TITLE
                referrer = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL
                jgspid = componentBean?.jgspid
                page_type = componentBean?.pageType
                page_id = componentBean?.pageId
                page_name = componentBean?.pageName
                page_no = componentBean?.pageNum?.toInt()
                page_size = componentBean?.pageSize?.toInt()
                component_position = componentBean?.componentPosition //组件序号
                component_name = componentBean?.componentName //组件名称
                component_title = componentBean?.componentTitle //组件标题
                sub_module_tab = data.showName //子模块tab
                product_id = data.productId.toLong() //商品ID
                product_name = data.productName //商品名称
                product_first = data.pId //商品一级分类ID
                product_price = data.productPrice.toDouble() //商品现价
                product_type = data.productType.toString() //商品类型
                product_shop_code = data.shopCode //商品店铺编码
                product_shop_name = data.shopName //商品店铺名称
            })
        }
        */

        private fun jgTrackProductClick(data: RowsBean) {
            var productTag = ""

            data.tags?.productTags?.let { tagList ->
                tagList.forEachIndexed { index, tagBean ->
                    if (index != tagList.size - 1) {
                        productTag += tagBean.text + "，"
                    } else {
                        productTag += tagBean.text
                    }
                }
            }
            data.tags?.dataTags?.let { tagList ->
                tagList.forEachIndexed { index, tagBean ->
                    if (index != tagList.size - 1) {
                        productTag += tagBean.text + "，"
                    } else {
                        productTag += tagBean.text
                    }
                }
            }
            // mBinding.root.context.jgTrackResourceProductClick(
            //     url = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL,
            //     module = JGTrackManager.Common.MODULE_PRODUCT_LIST,
            //     referrer = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL,
            //     pageId = JGTrackManager.TrackHomePage.PAGE_ID,
            //     title = JGTrackManager.TrackHomePage.TITLE,
            //     resourceId = "",
            //     resourceName = "",
            //     resourceType = "",
            //     position = bindingAdapterPosition,
            //     productId = data.productId ?: "",
            //     productName = data.productName ?: "",
            //     productType = data.jgProductType ?: "",
            //     productPrice = data.jgProductPrice ?: 0.0,
            //     productLabel = productTag,
            //     entrance = "首页(商品列表)",
            //     navigation = navigation
            // ) // 极光埋点移除：移除首页Feed流商品resource_click事件
        }

        //设置倒计时
        private fun processCountDown(
            mBinding: ItemFeedStreamCommodityBinding,
            rowsBean: RowsBean,
            countDownTimerMap: SparseArray<CountDownTimer>,
            countDownCallBack: ((Int) -> Unit)? = null
        ) {
            val currentTime = System.currentTimeMillis()
            val localDiff: Long = currentTime - (rowsBean.actSk?.responseLocalTime ?: 0L)
            val mSurplusTime = (rowsBean.actSk?.surplusTime ?: 0) - localDiff  //剩余时间
            LogUtil.d(
                TAG,
                "currentTime: $currentTime  localDiff: $localDiff   mSurplusTime:$mSurplusTime"
            )
            // 如果遇到刷新数据，要先取消原来的倒计时
            countDownTimerMap.get(this.hashCode())?.let {
                it.cancel()
                countDownTimerMap.remove(this.hashCode())
            }

            if (rowsBean.status == 2 || rowsBean.status == 4 || rowsBean.availableQty <= 0) {
                rowsBean.actSk?.status = 2
            }

            if (mSurplusTime >= 0 && (rowsBean.actSk?.status == 0 || rowsBean.actSk?.status == 1)) {
                // 秒杀倒计时
                val countDownTimer = object : CountDownTimer(mSurplusTime, 1000L) {
                    override fun onTick(millisUntilFinished: Long) {
                        var oneMinute = 60 * 1000L
                        var oneHour = 60 * oneMinute
                        var mHour: Long = millisUntilFinished / oneHour
                        var mMinute: Long = (millisUntilFinished % oneHour) / oneMinute
                        var mSecond: Long = (millisUntilFinished % oneMinute) / 1000L
                        val mHourStr = if (mHour < 10) "0$mHour" else "$mHour"
                        val mMinuteStr = if (mMinute < 10) "0$mMinute" else "$mMinute"
                        val mSecondStr = if (mSecond < 10) "0$mSecond" else "$mSecond"

                        mBinding.tvTime.text = "距结束 ${mHourStr}:${mMinuteStr}:${mSecondStr}"
                        mBinding.clFlashSaleCountdown.postInvalidate()

                    }

                    override fun onFinish() {
                        if (rowsBean.actSk?.status == 0) {
                            rowsBean.actSk?.status = 1
                        } else if (rowsBean.actSk?.status == 1) {
                            rowsBean.actSk?.status = 2
                        }
                        <EMAIL> {
                            // TODO: 李江 回调先放这里  目前是结束隐藏 部分空间
                            countDownCallBack?.invoke(<EMAIL>)
                            mBinding.clFlashSaleCountdown.isVisible = false
                            mBinding.clFlashSaleCountdown.postInvalidate()
//							adapter.notifyItemChanged(baseViewHolder.bindingAdapterPosition)
                        }
                    }
                }
                this.countDownTimerMap.put(this.hashCode(), countDownTimer)
                countDownTimer.start()
            }

            if (rowsBean.actSk?.status == 2) {
                mBinding.clFlashSaleCountdown.isVisible = false
            }

        }


        fun getAllTags(data: RowsBean): MutableList<TagBean> {
            var tags = mutableListOf<TagBean>()
            data.tags?.productTags?.also { tags += it }
            if (tags.size > 2) {
                tags = tags.subList(0, 2)
            }
            return tags
        }
    }

    inner class ADFeedStreamVH(
        val mBinding: ItemFeedStreamAdBinding,
        val clickCallBack: ((position: Int, url: String, clickType: Int, product: RowsBean?, componentBean: FeedComponentBean?) -> Unit)? = null,
        var mAdapter: HomeFeedStreamAdapter,
        val navigation: String,
        val componentBean: ComponentBean?
    ) : BaseViewHolder(mBinding.root) {

        private val resourceViewTrackMap = hashMapOf<String, Long>()

        fun bind(data: HomeFeedStreamBean.HomeFeedAD) {
            mBinding.apply {


                val mData = getAdvertisementDetailBean(data, mAdapter)
                root.context.glideLoadWithPlaceHolder(mData?.imageUrl ?: "", ivAd)

                root.setOnClickListener {
                    val jumpUrl = getAdvertisementDetailBean(data, mAdapter)?.hrefUrls ?: ""
                    clickCallBack?.invoke(
                        absoluteAdapterPosition,
                        jumpUrl,
                        AD_VIEW_TYPE,
                        null,
                        null
                    )
                    jgTrackADClick(mData)
                    onAdItemClick(mData?.trackData)
                }

                if (mData?.imageUrl.isNullOrEmpty()) {
                } else {
                    resourceViewTrackMap[mData?.imageUrl ?: ""]?.let {
                        if (System.currentTimeMillis() - it > TRACK_DURATION) {
                            resourceViewTrackMap[mData?.imageUrl ?: ""] = System.currentTimeMillis()
                        }
                    } ?: kotlin.run {
                        resourceViewTrackMap[mData?.imageUrl ?: ""] = System.currentTimeMillis()
                    }
                }
            }
        }

        private fun jgTrackADClick(data: HomeFeedStreamBean.AdvertisementDetailBean?) {
            mBinding.root.context.jgTrackResourceProductClick(
                url = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL,
                module = JGTrackManager.Common.MODULE_PRODUCT_LIST,
                referrer = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL,
                pageId = JGTrackManager.TrackHomePage.PAGE_ID,
                title = JGTrackManager.TrackHomePage.TITLE,
                resourceId = data?.activityId ?: "",
                resourceName = data?.activityName ?: "",
                resourceType = "广告",
                position = bindingAdapterPosition,
                productId = "",
                productName = "",
                productType = "",
                productPrice = 0.0,
                productLabel = "",
                entrance = "首页(商品列表)",
                navigation = navigation
            )
        }

        private fun getAdvertisementDetailBean(
            data: HomeFeedStreamBean.HomeFeedAD,
            mAdapter: HomeFeedStreamAdapter
        ): HomeFeedStreamBean.AdvertisementDetailBean? {
            return data.advertisementDetailList?.let { list ->
                var mPosition: Int
                if (list.size > 0) {
                    if (data.isNeedRandom()) { //
                        mPosition = Random.nextInt(0, list.size - 1)
                    } else { //顺序
                        mPosition = (mAdapter.mAdRefreshCount) % list.size
                    }

                    if (mPosition > list.size - 1) mPosition = 0 //以防万一
                    LogUtil.d(
                        TAG,
                        " adPostion: $absoluteAdapterPosition isRandom: ${data.isNeedRandom()} mAdRefreshCount:${mAdapter.mAdRefreshCount} "
                    )
                    list[mPosition]
                } else {
                    null
                }
            } ?: kotlin.run {
                null
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (mDataList[position].getFeedType()) {
            HomeFeedStreamBean.FEED_STREAM_BANNER_TYPE -> AD_BANNER_VIEW_TYPE
            HomeFeedStreamBean.FEED_STREAM_AD_TYPE -> AD_VIEW_TYPE
            HomeFeedStreamBean.FEED_STREAM_PRODUCT_TYPE -> COMMODITY_VIEW_TYPE
            else -> AD_BANNER_VIEW_TYPE
        }
    }

    override fun getItemCount(): Int = mDataList.size


    class HomeFeedStreamItemDecoration() : RecyclerView.ItemDecoration() {

        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)

            if (view.tag == "HomeFeedStreamAdapter") {
                val layoutManager: StaggeredGridLayoutManager =
                    (parent.layoutManager as? StaggeredGridLayoutManager)
                        ?: return
                val spanCount = layoutManager.spanCount
                val spanIndex =
                    (view.layoutParams as? StaggeredGridLayoutManager.LayoutParams)?.spanIndex
                        ?: return

                if (spanCount == 2 && spanIndex != GridLayoutManager.LayoutParams.INVALID_SPAN_ID) {

                    val marginLeft = if (spanIndex % 2 != 0) (3.5f).dp else 7.dp
                    val marginTop = 7.dp
                    val marginBottom = 0.dp
                    val marginRight = if (spanIndex % 2 != 0) 7.dp else (3.5f).dp

                    outRect.set(marginLeft, marginTop, marginRight, marginBottom)

                }
            }
        }
    }

}