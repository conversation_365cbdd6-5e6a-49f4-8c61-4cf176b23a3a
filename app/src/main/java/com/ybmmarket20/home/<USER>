package com.ybmmarket20.home

import KEY_SHOP_EXPAND_STATUS
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.graphics.drawable.AnimationDrawable
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.google.gson.Gson
import com.tencent.mmkv.MMKV
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.bean.TagBean
import com.ybmmarket20.bean.VoucherListBean
import com.ybmmarket20.bean.cart.CartBean.NeedToBePerfectedActBean
import com.ybmmarket20.bean.cart.CartBeanWraper
import com.ybmmarket20.bean.cart.CartDiscountDataBean
import com.ybmmarket20.bean.cart.CollectionInvalidGoodsInfo
import com.ybmmarket20.bean.cart.INVALID_GOODS_PACKAGE
import com.ybmmarket20.bean.cart.Level0ItemShopFooterBean
import com.ybmmarket20.bean.cart.Level0ItemShopHeaderBean
import com.ybmmarket20.bean.cart.Level0ItemWrapper
import com.ybmmarket20.bean.cart.Level1InvalidItemGoodsBeanAbs
import com.ybmmarket20.bean.cart.Level1ItemGoodsBeanAbs
import com.ybmmarket20.bean.cart.Level1ItemGroupFooterBean
import com.ybmmarket20.bean.cart.Level1ItemGroupGoodBean
import com.ybmmarket20.bean.cart.Level1ItemGroupHeaderBean
import com.ybmmarket20.bean.cart.Level1ItemWrapper
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JGTrackManager.Companion.eventTrack
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.jgTrackResourceProductClick
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.report.coupon.CartAddGoodCouponEntryType
import com.ybmmarket20.report.coupon.CartShopCouponEntryType
import com.ybmmarket20.report.coupon.CartTopCouponCartEntryType
import com.ybmmarket20.report.coupon.ICouponCartEntryType
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.utils.AdapterUtils
import com.ybmmarket20.utils.CartLoading
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.flowDataPageCommoditySearch
import com.ybmmarket20.view.BaseBottomPopWindow
import com.ybmmarket20.view.FreightTipDialog
import com.ybmmarket20.view.GiftSelectBottomDialog
import com.ybmmarket20.view.ShowBottomCartCouponDialog
import com.ybmmarket20.view.ShowFreightPopWindow
import com.ybmmarket20.viewmodel.BaseViewModel
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import com.ybmmarketkotlin.utils.AptitudeTipsUtils.Companion.initAptitudeOverdueTip
import com.ybmmarketkotlin.utils.TextWithPrefixTag
import com.ybmmarketkotlin.viewmodel.CartViewModel3
import kotlinx.android.synthetic.main.fragment_cart_v3.btn_commit
import kotlinx.android.synthetic.main.fragment_cart_v3.cart_tv_edit
import kotlinx.android.synthetic.main.fragment_cart_v3.cl_bottom
import kotlinx.android.synthetic.main.fragment_cart_v3.cl_cart_bottom_tips
import kotlinx.android.synthetic.main.fragment_cart_v3.fl_cart_discount
import kotlinx.android.synthetic.main.fragment_cart_v3.header_layout
import kotlinx.android.synthetic.main.fragment_cart_v3.iv_cart_loading
import kotlinx.android.synthetic.main.fragment_cart_v3.layout_aptitude_tip
import kotlinx.android.synthetic.main.fragment_cart_v3.ll_cart_total_loading
import kotlinx.android.synthetic.main.fragment_cart_v3.ll_freight_over_weight_tips
import kotlinx.android.synthetic.main.fragment_cart_v3.rtv_over_weight_look
import kotlinx.android.synthetic.main.fragment_cart_v3.rv_shop_list
import kotlinx.android.synthetic.main.fragment_cart_v3.shop_check
import kotlinx.android.synthetic.main.fragment_cart_v3.smartrefresh
import kotlinx.android.synthetic.main.fragment_cart_v3.tv_bottom_tips_action
import kotlinx.android.synthetic.main.fragment_cart_v3.tv_cart_bottom_tips
import kotlinx.android.synthetic.main.fragment_cart_v3.tv_cart_discount_detail
import kotlinx.android.synthetic.main.fragment_cart_v3.tv_cart_promotion_tips
import kotlinx.android.synthetic.main.fragment_cart_v3.tv_cart_total
import kotlinx.android.synthetic.main.fragment_cart_v3.tv_cart_total_loading
import kotlinx.android.synthetic.main.fragment_cart_v3.tv_coupon_cross_shop
import kotlinx.android.synthetic.main.fragment_cart_v3.tv_edit
import kotlinx.android.synthetic.main.fragment_cart_v3.tv_over_weight_tips
import kotlinx.android.synthetic.main.fragment_cart_v3.tv_title_name
import kotlinx.android.synthetic.main.layout_aptitude_overdue_tip.tv_aptitude_tip
import kotlin.collections.set

class CartFragmentV3 : CartFragmentAnalysisV3(), CartFragmentCommonImpl, ICouponCartEntryType {


    val mViewModel: CartViewModel3 by viewModels()
    lateinit var concatAdapter: ConcatAdapter

    private val goodlist: MutableList<MultiItemEntity> = mutableListOf()
    lateinit var goodsAdapter: CartItemAdapter3

    private val recommendHeader: MutableList<Int> = mutableListOf(1)
    lateinit var recommendHeaderAdapter: RecommendHeaderAdapter

    private val recommendGoodsList: MutableList<RowsBean> = mutableListOf()
    lateinit var recommondGoodsAdapter: GoodListAdapterNew

    private var mDiscountData: CartDiscountDataBean? = null
    private var mCartDiscountUtils: CartDiscountUtils? = null

    private var commitText: String? = null
    private var cartLoading: CartLoading? = null

    private var item_shop_header: ConstraintLayout? = null
    val mCartAnalysis: CartAnalysis by lazy { CartAnalysis() }
    var loadingItems = listOf<Level0ItemShopFooterBean>()

    private var mBizSource = 0
    private var backTitleName: String? = null
    private var backTitleAction: String? = null

    companion object {
        private const val JG_TRACK_URL = "com.ybmmarket20.home.CartFragmentV3"

        fun jgBtnClickTrack(mContext: Context, btnName: String) {
            val mParams = java.util.HashMap<String, String>()
            mParams[JGTrackManager.FIELD.FIELD_URL] = this.getFullClassName()
            mParams[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = this.getFullClassName()
            mParams[JGTrackManager.FIELD.FIELD_REFERRER] = this.getFullClassName()
            mParams[JGTrackManager.FIELD.FIELD_REFERRER_TITLE] = JGTrackManager.TrackShoppingCart.TITLE
            mParams[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackShoppingCart.PAGE_ID
            mParams[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackShoppingCart.TITLE
            mParams[JGTrackManager.FIELD.FIELD_MODULE] = "功能"
            mParams[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName

            // 移除极光埋点 - btn_click
            /*
            eventTrack(
                    mContext,
                    JGTrackManager.TrackMineSupplier.EVENT_BTN_CLICK,
                    mParams)
            */
        }
    }

    override fun getRootLayoutParam(): ViewGroup.LayoutParams {
        return ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.MATCH_PARENT, 1)
    }

    fun updateCartData(cartBeanWraper: CartBeanWraper) {
        mBizSource = cartBeanWraper.bizSource
        btn_commit?.isEnabled = !cartBeanWraper.isLoading
        smartrefresh?.finishRefresh()
        goodlist.clear()
        cartBeanWraper.cartEntityList.forEach {
            if (it is Level0ItemShopHeaderBean && goodsAdapter.cachedShopExpandStatus[it.shopCode] != false) {
                it.isExpanded = true
                goodlist.add(it)
                goodlist.addAll(it.subItems)
            } else {
                goodlist.add(it)
            }
        }
        if (goodlist.isEmpty()) {
            cl_bottom.visibility = View.GONE
        } else {
            cl_bottom.visibility = View.VISIBLE
        }
        tv_title_name?.text = "购物车(${cartBeanWraper.varietyNum})"
        if (cartBeanWraper.isLoading) {
            ll_cart_total_loading?.visibility = View.VISIBLE
            tv_cart_total?.visibility = View.GONE
            if (cartLoading == null) {
                cartLoading = CartLoading()
            }
            handleFooterLoading(cartBeanWraper)
            cartLoading?.startCartLoading {
                 val loadingText = when(it) {
                    1 -> "计算中."
                    2 -> "计算中.."
                    else -> "计算中..."
                }
                tv_cart_total_loading?.text = loadingText
                if (loadingItems.isNotEmpty()) {
                    loadingItems.forEach { footer ->
                        footer.loadingText = loadingText
                    }
                    goodsAdapter.notifyDataSetChanged()
                }
            }
        } else {
            ll_cart_total_loading?.visibility = View.GONE
            tv_cart_total?.visibility = View.VISIBLE
            cartLoading?.stopCartLoading()
            tv_cart_total?.text = cartBeanWraper.totalAmount
        }
        tv_cart_promotion_tips?.text = cartBeanWraper.discountsStr

        commitText = cartBeanWraper.commitText
        btn_commit?.text = if (tv_edit?.isChecked == true) CartBeanWraper.DELETE else commitText
        btn_commit?.setBackgroundColor(if (tv_edit?.isChecked == true) resources.getColor(R.color.cart_bottom_del) else resources.getColor(R.color.base_colors))
        btn_commit?.setOnClickListener {
            if (tv_edit?.isChecked != true){
                trackClickToPay(btn_commit.text?.toString())
            }
            if (!cartBeanWraper.needToBePerfectedActList.isNullOrEmpty()){

                val giftSelectBottomDialog: GiftSelectBottomDialog = if (cartBeanWraper.needToBePerfectedActList.size == 1) {
                    val bean: NeedToBePerfectedActBean = cartBeanWraper.needToBePerfectedActList[0]
                    GiftSelectBottomDialog(requireActivity(), bean.giftPoolActTotalSelectedNum, bean.promoId, mBizSource)
                } else {
                    GiftSelectBottomDialog(requireActivity(), ArrayList(cartBeanWraper.needToBePerfectedActList), mBizSource)
                }

                giftSelectBottomDialog.confirmClickCallBack = {
                    todoBtnCommitClick(cartBeanWraper)
                }
                giftSelectBottomDialog.closeCallBack = {
//                    todoBtnCommitClick(cartBeanWraper)
                    showCartLoading()
                    mViewModel.getCartData(SpUtil.getMerchantid())
                }
                giftSelectBottomDialog.show()

            }else{
                todoBtnCommitClick(cartBeanWraper)
            }
        }

        shop_check?.isChecked = cartBeanWraper.isSelected

        if (needRefreshStickyHeaderData) {
            updateShopHeader(currentStickyHeaderPosition)
            needRefreshStickyHeaderData = false
        } else {
//            currentStickyHeaderPosition = -1
            cacheShopHeaderIndex.clear()
        }
        goodsAdapter.notifyDataSetChanged()

        // 可凑单参加的促销提醒
        if (TextUtils.isEmpty(cartBeanWraper?.crossStoreVoucherDto?.crossStoreVoucherTips)) {
            cl_cart_bottom_tips?.visibility = View.GONE
        } else {
            cl_cart_bottom_tips?.visibility = View.VISIBLE
            cartBeanWraper?.crossStoreVoucherDto?.apply {
                tv_cart_bottom_tips?.text = crossStoreVoucherTips
                tv_bottom_tips_action?.setOnClickListener {
                    mViewModel.getVoucher(this.nextLadderVoucherId, chooseUrl, getCouponCartAddGoodsType().getCouponEntryType())
                    jgBtnClickTrack(requireActivity(),"黄条去凑单")
                }
            }
        }

        // 超重提示
        ll_freight_over_weight_tips?.visibility = if (cartBeanWraper.specialTipsShow) View.VISIBLE else View.GONE
        rtv_over_weight_look?.text = cartBeanWraper.getSpecialProductUrlTitle() ?: "${getString(R.string.str_freight_over_weight_cart_bottom_look)}"
        tv_over_weight_tips?.text = cartBeanWraper.getSpecialProductTips() ?: "${getString(R.string.str_freight_over_weight_cart_bottom_tip)}"
        rtv_over_weight_look.setOnClickListener {
            RoutersUtils.open("ybmpage://freightoverweightgoods")
        }

        // 以满足优惠明细内容更新
        mDiscountData = cartBeanWraper?.promoAmountDto
        if (mDiscountData == null) {
            tv_cart_discount_detail?.visibility = View.GONE
        } else {
            mCartDiscountUtils?.updateData(mDiscountData)
            if (tv_edit?.isChecked == false) tv_cart_discount_detail?.visibility = View.VISIBLE
        }

        dismissProgress()
    }

    private fun todoBtnCommitClick(cartBeanWraper: CartBeanWraper) {
        when (btn_commit?.text) {
            CartBeanWraper.CLOSE_AN_ACCOUNT -> {
                mCartAnalysis.cartSettleClick("2")
                toPreSettle(cartBeanWraper)
            }

            CartBeanWraper.CLOSE_AN_ACCOUNT_COUPON -> {
                mCartAnalysis.cartSettleClick("1")
                toPreSettle(cartBeanWraper)
            }

            CartBeanWraper.DELETE -> {
                // 编辑状态,删除商品
                showDelDialog(cartBeanWraper.cartEntityList)

            }
        }

        try {
            val map = HashMap<String, Any>()
            map[JGTrackManager.FIELD.FIELD_URL] = JG_TRACK_URL
            map[JGTrackManager.FIELD.FIELD_REFERRER] = JG_TRACK_URL
            map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackShoppingCart.PAGE_ID
            map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackShoppingCart.TITLE
            map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btn_commit?.text ?: ""
            // 移除极光埋点 - btn_click
            // eventTrack(requireActivity(), JGTrackManager.TrackShoppingCart.EVENT_BTN_CLICK, map)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun handleFooterLoading(cartBeanWraper: CartBeanWraper) {
        loadingItems = cartBeanWraper.cartEntityList.filterIsInstance<Level0ItemShopFooterBean>()
            .filter { it.isLoading }
    }

    override fun initData(content: String?) {

        val block: CartItemAdapter3.() -> Unit = {
            val emptyView = View.inflate(context, R.layout.empty_view_cart, null)
            val layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            emptyView.layoutParams = layoutParams
            setEmptyView(emptyView)
            jgTrackBean = JgTrackBean(
                    jgReferrer = <EMAIL>(),
                    jgReferrerTitle = JGTrackManager.TrackShoppingCart.TITLE,
                    jgReferrerModule = JGTrackManager.TrackShoppingCart.TITLE,
                    module = JGTrackManager.TrackShoppingCart.TITLE,
                    pageId = JGTrackManager.TrackShoppingCart.PAGE_ID,
                    title = JGTrackManager.TrackShoppingCart.TITLE,
                    entrance = JGTrackManager.TrackShoppingCart.TITLE
            )

            resourceViewTrackListener = { productId,productName,productPrice, position ->

            }

            resourceClickTrackListener = { productId, productName, productPrice, position ->
                requireActivity().jgTrackResourceProductClick(
                        url = <EMAIL>(),
                        module = JGTrackManager.Common.MODULE_PRODUCT_LIST,
                        referrer = <EMAIL>(),
                        pageId = JGTrackManager.TrackShoppingCart.PAGE_ID,
                        title = JGTrackManager.TrackShoppingCart.TITLE,
                        resourceId = "",
                        resourceName = "",
                        resourceType = "",
                        position = position,
                        productId = productId,
                        productName = productName,
                        productType = "普通商品",
                        productPrice = productPrice,
                        productLabel = "",
                        entrance = jgTrackBean?.entrance ?: "",
                        navigation = ""
                )
            }

            mGiftSelectClickListener = { level1ItemActivityGiftSelectBean ->

                GiftSelectBottomDialog(
                        requireActivity(),
                        level1ItemActivityGiftSelectBean.cartShoppingGroupFrontBean?.giftPoolActTotalSelectedNum
                                ?: 0,
                        level1ItemActivityGiftSelectBean.cartShoppingGroupFrontBean?.promoId
                                ?: "",
                        mBizSource).apply {

                    confirmClickCallBack = {
                        showCartLoading()
                        mViewModel.getCartData(SpUtil.getMerchantid())
                    }

                    closeCallBack = {
                        showCartLoading()
                        mViewModel.getCartData(SpUtil.getMerchantid())
                    }
                        
                    show()
                }
            }
        }
        goodsAdapter = CartItemAdapter3(goodlist, this).apply(block)

        val decodeString = MMKV.defaultMMKV().decodeString("${KEY_SHOP_EXPAND_STATUS}_${SpUtil.getMerchantid()}", "{}")
        goodsAdapter.cachedShopExpandStatus = Gson().fromJson(decodeString, goodsAdapter.cachedShopExpandStatus::class.java)


        recommendHeaderAdapter = RecommendHeaderAdapter(R.layout.item_cart_recommend_header, recommendHeader)

        recommondGoodsAdapter = GoodListAdapterNew(R.layout.item_goods_new, recommendGoodsList).apply {
            jgTrackBean = JgTrackBean(
                    jgReferrer = <EMAIL>(),
                    jgReferrerTitle = JGTrackManager.TrackShoppingCart.TITLE,
                    jgReferrerModule = JGTrackManager.TrackShoppingCart.TITLE,
                    module = "${JGTrackManager.TrackShoppingCart.TITLE}-热销精选",
                    pageId = JGTrackManager.TrackShoppingCart.PAGE_ID,
                    title = JGTrackManager.TrackShoppingCart.TITLE,
                    entrance = "${JGTrackManager.TrackShoppingCart.TITLE}(热销精选)",
                    url = <EMAIL>()
            )

            resourceViewTrackListener = { rowsBean, i,_ ->
                var productTag = ""
                rowsBean.tags?.productTags?.let { tagList ->
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
            }

            productClickTrackListener = { rowsBean, i,isBtnClick,mContent,number ->
                var productTag = ""
                rowsBean.tags?.productTags?.let { tagList ->
                    if (productTag.isNotEmpty()) {
                        productTag += ","
                    }
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }

                requireActivity().jgTrackResourceProductClick(
                        url = <EMAIL>(),
                        module = JGTrackManager.Common.MODULE_PRODUCT_LIST,
                        referrer = <EMAIL>(),
                        pageId = JGTrackManager.TrackShoppingCart.PAGE_ID,
                        title = JGTrackManager.TrackShoppingCart.TITLE,
                        resourceId = "",
                        resourceName = "",
                        resourceType = "",
                        position = i,
                        productId = rowsBean.productId ?:"",
                        productName = rowsBean.productName?:"",
                        productType = rowsBean.jgProductType?:"",
                        productPrice = rowsBean.jgProductPrice ?: 0.0,
                        productLabel = productTag,
                        entrance = jgTrackBean?.entrance?:"",
                        navigation = ""
                )
            }
        }
        concatAdapter = ConcatAdapter(listOf(goodsAdapter, recommendHeaderAdapter, recommondGoodsAdapter))

        rv_shop_list?.adapter = concatAdapter
        rv_shop_list?.layoutManager = LinearLayoutManager(context)
        rv_shop_list?.itemAnimator?.changeDuration = 0
        (rv_shop_list?.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
        addStickyHeaderForCart()


        mViewModel.cartBean.observe(this, Observer { cartBeanWraper ->
            goodsAdapter.onClear()
            updateCartData(cartBeanWraper)
            dismissCartLoading()
        })

        mViewModel.batchcollectResultBean.observe(this, Observer {
            if (it.isSuccess) {
                mViewModel.getCartData(SpUtil.getMerchantid())
                ToastUtils.showLong(it.msg)
            } else {
                dismissProgress()
            }
        })

        tv_edit?.setOnCheckedChangeListener { _, isChecked ->
            tv_edit?.text = if (isChecked) CartBeanWraper.ACCOMPLISH else CartBeanWraper.EDIT
            if (isChecked) {
                cl_bottom?.setBackgroundColor(resources.getColor(R.color.cart_bottom_edit_bg))
                cart_tv_edit?.setTextColor(resources.getColor(R.color.white))
                btn_commit?.setBackgroundColor(resources.getColor(R.color.cart_bottom_del))
                btn_commit?.text = CartBeanWraper.DELETE
                tv_cart_total?.visibility = View.GONE
                tv_cart_promotion_tips?.visibility = View.GONE
                tv_cart_discount_detail?.visibility = View.GONE
                //点击了编辑 变成编辑状态
                jgBtnClickTrack(requireActivity(),CartBeanWraper.EDIT)
            } else {
                cl_bottom?.setBackgroundColor(resources.getColor(R.color.white))
                cart_tv_edit?.setTextColor(resources.getColor(R.color.color_292933))
                btn_commit?.setBackgroundColor(resources.getColor(R.color.base_colors))
                btn_commit?.text = commitText
                tv_cart_total?.visibility = View.VISIBLE
                tv_cart_promotion_tips?.visibility = View.VISIBLE
                tv_cart_discount_detail?.visibility = View.VISIBLE

                //点击完成
                jgBtnClickTrack(requireActivity(),CartBeanWraper.ACCOMPLISH)
            }
        }

        shop_check?.setOnClickListener {
            showProgress()
            mViewModel.changeAllSelectStatus(shop_check?.isChecked == true)
            jgBtnClickTrack(requireActivity(),"全选")
        }

        mViewModel.onlyStatusResultBean.observe(this, Observer {
            if (it) {
                if (!isShowingProgress) {
                    showProgress()
                }
                mViewModel.getCartData(SpUtil.getMerchantid())
            } else {
                dismissProgress()
            }
        })

        mViewModel.showToastLiveData.observe(this, Observer {
            ToastUtils.showShort(it)
        })

        mViewModel.onlyGoodStatusResultBean.observe(this, Observer {
            if (it) {
                showProgress()
                needRefreshStickyHeaderData = true
                mViewModel.getCartData(SpUtil.getMerchantid())
            } else {
                dismissProgress()
            }
        })

        smartrefresh?.setOnRefreshListener {
            header_layout?.visibility = View.GONE
            trackPv()
            trackBottomTabExposure()
            mViewModel.getCartData(SpUtil.getMerchantid())
        }
        smartrefresh?.setOnLoadMoreListener {
            page++
            getRecommendData()
        }
        showProgress()
        mViewModel.getCartData(SpUtil.getMerchantid())
//        smartrefresh.autoRefresh()


        tv_coupon_cross_shop?.setOnClickListener {
            trackClickTopCoupon()
            mCartAnalysis.cartCrossShopCouponClick()
            val showBottomCartCouponDialog = ShowBottomCartCouponDialog(getCouponCartTopEntryType(),"","","",VoucherListBean.QUERY_COUPON_TYPE_PLATFORM,JgTrackBean(
                    jgReferrer = <EMAIL>(),
                    jgReferrerTitle = JGTrackManager.TrackShoppingCart.TITLE,
                    jgReferrerModule = JGTrackManager.TrackShoppingCart.TITLE,
                    module = "${JGTrackManager.TrackShoppingCart.TITLE}",
                    pageId = JGTrackManager.TrackShoppingCart.PAGE_ID,
                    title = JGTrackManager.TrackShoppingCart.TITLE,
                    url = <EMAIL>(),
                    entrance = JGTrackManager.TrackShoppingCart.TITLE)).apply {

                setOnSelectListener(object : BaseBottomPopWindow.OnSelectListener {
                    override fun getValue(show: SearchFilterBean?) {

                    }

                    override fun OnDismiss() {
                        mViewModel.getCartData(SpUtil.getMerchantid())
                    }

                })
            }
            showBottomCartCouponDialog.show(it)
            jgBtnClickTrack(requireActivity(),"跨店券")
        }

        // 底部促销、优惠券 优惠明细
        mCartDiscountUtils = CartDiscountUtils(fl_cart_discount, cl_bottom, tv_cart_discount_detail, mDiscountData)
        tv_cart_discount_detail?.setOnClickListener {
            mCartAnalysis.cartDetailClick()
            mCartDiscountUtils?.dispatchDisCountAnim()
            jgBtnClickTrack(requireActivity(),"明细")
        }

        // 初始化设置超重提示
        setFreightPopwindow()

        mViewModel.recommendData.observe(this) { data ->
            data?.let { refreshWrapperPagerBean ->
                data.rows.let {
                    recommendGoodsList.addAll(it ?: mutableListOf())
                    recommendGoodsList.forEach {
                        it.isCart = true
                    }
                    // 请求并更新折后价
//                    getAfterDiscountPrice(it, recommondGoodsAdapter)
                    AdapterUtils.updateRowsData(
                        refreshWrapperPagerBean.licenseStatus,
                        refreshWrapperPagerBean.rows ?: mutableListOf(),
                        recommondGoodsAdapter,
                        false,
                        refreshWrapperPagerBean.isEnd
                    )
                }
                mRequestMap = refreshWrapperPagerBean.requestParam
                if (isLoadData) {
                    smartrefresh?.finishLoadMore()
                }
                if (!isLoadData) {
                    isLoadData = true
                    mFlowData.sId = data.sid
                    mFlowData.spId = data.spId
                    mFlowData.spType = data.spType
                    flowDataPageCommoditySearch(mFlowData)
                    recommondGoodsAdapter.flowData = mFlowData
                }
                recommondGoodsAdapter.notifyDataSetChanged()
            }
        }
        mViewModel.batchCollectWithDataInfoLiveData.observe(this, Observer {
            if (it.isSuccess) {
                if (it.data.packageState == INVALID_GOODS_PACKAGE) {
                    //失效套餐
                    val goodsIdsList = it.data.ids?.split(",")
                    if (goodsIdsList?.size?: 0 > 1) {
                        dismissProgress()
                        // 失效套餐商品数量大于1，弹窗提示
                        AlertDialogEx(context)
                            .setTitle("提示")
                            .setMessage("将收藏整个套餐中的商品，是否收藏")
                            .setCanceledOnTouchOutside(false)
                            .setCancelButton("我再想想") { dialog, _ ->
                                dialog.dismiss()
                            }.setConfirmButton("确认") { dialog, _ ->
                                dialog.dismiss()
                                showProgress()
                                mViewModel.batchCollectAndRemoveInvalidPackage(it.data)
                            }.show()
                    } else {
                        removeGoodsAfterCollectGoods(it.data)
                    }
                } else {
                    //单个失效商品、失效商品全部移入收藏
                    removeGoodsAfterCollectGoods(it.data)
                }
            } else {
                dismissProgress()
            }
        })
        mViewModel.uiLiveData.observe(this, Observer{
            showCartLoading()
            needRefreshStickyHeaderData = true
            it?.let { it1 -> updateCartData(it1) }
        })
        getRecommendData()
        initReceiver()
    }

    private fun showCartLoading() {
        if (iv_cart_loading.visibility != View.VISIBLE) {
            iv_cart_loading.visibility = View.VISIBLE
            iv_cart_loading.setBackgroundResource(R.drawable.cart_loading)
            val animDrawable = iv_cart_loading.background as? AnimationDrawable
            animDrawable?.start()
        }
    }

    private fun dismissCartLoading() {
        if (iv_cart_loading.visibility == View.VISIBLE) {
            iv_cart_loading.visibility = View.GONE
        }
    }



    /**
     * 收藏商品后移除商品
     */
    fun removeGoodsAfterCollectGoods(collectionInvalidInfo: CollectionInvalidGoodsInfo) {
        mViewModel.removeProductFromCart(SpUtil.getMerchantid(), collectionInvalidInfo.packageId, collectionInvalidInfo.ids
                ?: "")
    }

    /**
     * 删除商品前的二次确认
     */
    fun showDelDialog(cartEntityList: MutableList<MultiItemEntity>) {
        val titleStr = "提示"
        val messageStr = "你确定删除该商品吗?"
        val cancelStr = "我再想想"
        val confirmStr = "确定"

        val alert = AlertDialogEx(notNullActivity).apply {
            setTitle(titleStr)
            setMessage(messageStr)
            setCancelButton(cancelStr, null)
            setCancelButtonTextColor(resources.getColor(R.color.color_292933))
            setConfirmButton(confirmStr) { dialog, button ->
                deleteOrCheckOutShop(cartEntityList)
            }
        }
        alert.show()
    }

    private fun toPreSettle(cartBeanWraper: CartBeanWraper) {
        if (cartBeanWraper.selectNum > 0) {
            // 提交状态
            popWindowPackage.setContent(cartBeanWraper)
            if (cartBeanWraper?.canSettle == 2 || cartBeanWraper?.canSettle == 3) {
                popWindowPackage.show(btn_commit)
            } else {
                showProgress()
                mViewModel.preSettle(null, context);
            }
        } else {
            UiUtils.toast("您还没有选择商品哦")
        }
    }

    var needRefreshStickyHeaderData = false
    var currentStickyHeaderPosition: Int = -1
    var cacheShopHeaderIndex: MutableList<Int> = mutableListOf<Int>()


    private fun addStickyHeaderForCart() {
        View.inflate(context, R.layout.item_cart_shop_header, header_layout)
        item_shop_header = header_layout?.findViewById(R.id.item_shop_header)

        var canScroll = false
        var headerHeight = 0
        var underLinePostion = 0
        var upScrollDistance = 0
        var headerTop = 0

        rv_shop_list.addOnScrollListener(object : RecyclerView.OnScrollListener() {

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                val maxNeedStickySize = goodlist.size
                if (maxNeedStickySize == 0) return

                var lastFootIndex = -1
                val lastFooterBean = goodlist.findLast { it is Level0ItemShopFooterBean }
                lastFooterBean?.let { lastFootIndex = goodlist.indexOf(lastFooterBean) }?.takeIf { lastFootIndex <= 0 }?.let { return }

                goodlist.filterIsInstance<Level0ItemWrapper>().size.takeIf { it <= 0 }?.let { return }
                val child = recyclerView.getChildAt(0)
                val layoutParams = child?.layoutParams as? RecyclerView.LayoutParams
                    ?: return
                val absolutePostion = layoutParams.absoluteAdapterPosition
                if (absolutePostion > maxNeedStickySize - 1 || absolutePostion < 0) return

                if (dy > 0) {
                    // 上滑
                    if (absolutePostion > currentStickyHeaderPosition
                        && header_layout?.visibility == View.VISIBLE
                        && canScroll == false
                    ) {
                        headerHeight = header_layout?.height ?: 0
                        val childView = findChildViewUnder(rv_shop_list, headerHeight)
                        val newUnderLinePostion = childView?.let { rv_shop_list.getChildAdapterPosition(it) } ?: -1
                        if (newUnderLinePostion > underLinePostion && goodlist.size > newUnderLinePostion) {
                            underLinePostion = newUnderLinePostion
                            if (underLinePostion > 0 && goodlist[underLinePostion] is Level0ItemShopFooterBean) {
                                canScroll = true
                                headerHeight = header_layout?.height ?: 0
                            }
                        }
                    }
                    if (goodlist[absolutePostion] is Level0ItemShopFooterBean && header_layout.visibility == View.VISIBLE) {
                        header_layout?.requestLayout()
                        header_layout.visibility = View.GONE
                        underLinePostion = 0
                        canScroll = false
                    }
                    if (
                        header_layout?.visibility == View.GONE &&
                        goodlist[absolutePostion] is Level0ItemShopHeaderBean
                        && !(goodlist[absolutePostion + 1] is Level0ItemShopFooterBean)
                    ) {
                        header_layout.visibility = View.VISIBLE
                        currentStickyHeaderPosition = absolutePostion
                        if (!cacheShopHeaderIndex.contains(currentStickyHeaderPosition)) {
                            cacheShopHeaderIndex.add(currentStickyHeaderPosition)
                        }
                        updateShopHeader(currentStickyHeaderPosition)

                        if (header_layout.visibility == View.VISIBLE && headerTop == 0) {
                            header_layout?.postDelayed({
                                headerTop = header_layout?.top ?: 0
                            }, 50)
                        }

                        if (absolutePostion >= maxNeedStickySize - 1) {
                            if (header_layout?.visibility == View.VISIBLE) {
                                header_layout.visibility = View.GONE
                                canScroll = false
                                //println("xyd header_layout_top = ${header_layout?.top} downScroll stop")
                            }
                        }
                    }

                    if (canScroll) {
                        ViewCompat.offsetTopAndBottom(header_layout, -dy)
                        //println("xyd header_layout_top = ${header_layout?.top} downScroll onstroll")
                    }
                } else {
                    //
                    // println("adapter scroll abc = ${absolutePostion} canscrollVertical = ${!recyclerView.canScrollVertically(-1)} vi = ${header_layout?.visibility == View.VISIBLE}")
                    if (absolutePostion == 0 && !recyclerView.canScrollVertically(-1) && header_layout?.visibility == View.VISIBLE) {
                        header_layout.visibility = View.GONE
                        underLinePostion = 0
                        canScroll = false
                        currentStickyHeaderPosition = -1
                        //println("adapter scroll abc = end up croll")
                        return
                    }
                    // 第一次拉到店铺尾部上方，开始可以滑动，初始化悬浮（设置向上偏移H,逐渐拉下来），
                    if (absolutePostion < lastFootIndex
                        && currentStickyHeaderPosition >= 0
                        && goodlist[absolutePostion] is Level1ItemWrapper
                        && goodlist[absolutePostion + 1] is Level0ItemShopFooterBean
                        && header_layout.visibility == View.GONE
                        && canScroll == false
                    ) {
                        header_layout.visibility = View.VISIBLE
                        header_layout?.alpha = 0f
                        val findLast = goodlist.findLast { it is Level0ItemShopHeaderBean && goodlist.indexOf(it) < absolutePostion }
                        currentStickyHeaderPosition = goodlist.indexOf(findLast)
                        updateShopHeader(currentStickyHeaderPosition)
                        //println("adapter scroll abc  cu = ${currentStickyHeaderPosition} headertop = ${header_layout.top}")

                        header_layout?.post {
                            //println("xyd header_layout_top = ${header_layout?.top} upScrollDistance=${upScrollDistance} start")
                            ViewCompat.offsetTopAndBottom(header_layout, -header_layout.height)
                            header_layout?.alpha = 1f
                        }
                        canScroll = true
                        headerHeight = header_layout.height
                    }

                    // 滑动到店铺头部上方，隐藏
                    if (
                        absolutePostion > 0
                        && absolutePostion < lastFootIndex
                        && goodlist[absolutePostion] is Level0ItemShopFooterBean
                        && header_layout.visibility == View.VISIBLE
                    ) {
                        canScroll = false
                        header_layout.visibility = View.INVISIBLE
                        header_layout.visibility = View.GONE
                    }

                    // 滑动到店铺尾部上方，更新可滑动状态
                    if (canScroll) {
                        ViewCompat.offsetTopAndBottom(header_layout, -dy)
                        upScrollDistance += Math.abs(dy)
                        //println("xyd header_layout_top = ${header_layout?.top} upScrollDistance=${upScrollDistance} onSroll")
                        if (upScrollDistance >= headerHeight && headerHeight > 0) {
                            canScroll = false
                            println("xyd header_layout_top = ${header_layout?.top} headerTop = ${headerTop} upScrollDistance=${upScrollDistance} endSroll")
                            var endDistance = ((header_layout?.top ?: 0) - headerTop)
                            println("xyd header_layout_top = endDistance = ${endDistance}")
                            ViewCompat.offsetTopAndBottom(header_layout, -endDistance)
                            upScrollDistance = 0
                        }
                    }
                }
            }

        })
    }

    fun findChildViewUnder(recyclerView: RecyclerView, y: Int): View? {
        val childCount = recyclerView.childCount
        if (childCount == 0) return null
        for (i in 0..childCount - 1) {
            val child = recyclerView.getChildAt(i);
            val translationY = child.getTranslationY();
            if (y >= child.getTop() + translationY
                && y <= child.getBottom() + translationY
            ) {
                return child;
            }
        }
        return null
    }


    /**
     * 处理店铺头部信息
     */
    private fun updateShopHeader(position: Int) {

        var positionCopy = position

        val tv_shop_name = header_layout?.findViewById<TextView>(R.id.tv_shop_name)
        val cbExpand = header_layout?.findViewById<CheckBox>(R.id.cb_expand)
        val iv_coupon = header_layout?.findViewById<ImageView>(R.id.iv_coupon)
        val cb_shop = header_layout?.findViewById<CheckBox>(R.id.cb_shop)

        val tv_freight = header_layout?.findViewById<TextView>(R.id.tv_freight)
        val tv_freight_arrow = header_layout?.findViewById<TextView>(R.id.tv_freight_arrow)

        val tv_refund = header_layout?.findViewById<TextView>(R.id.tv_refund)
        val tv_refund_arrow = header_layout?.findViewById<TextView>(R.id.tv_refund_arrow)
        val tv_shop_discounts = header_layout.findViewById<TextView>(R.id.tv_shop_discounts)
        val tv_shop_discounts_arrow = header_layout.findViewById<TextView>(R.id.tv_shop_discounts_arrow)
        val iv_shop_discounts = header_layout.findViewById<ImageView>(R.id.iv_shop_discounts)

        if (positionCopy < 0) return
        if (goodsAdapter.data.size <= positionCopy || !(goodsAdapter.data[positionCopy] is Level0ItemShopHeaderBean)) {
            return
        }
        val level0ItemShopHeaderBean = goodsAdapter.data[positionCopy] as Level0ItemShopHeaderBean

        level0ItemShopHeaderBean?.apply {
            var tagBean: TagBean? = null
            if (!isThirdCompany) {
                tagBean = TagBean().apply {
                    uiStyle = 1
                    text = "自营"
                    textColor = "#00B377"
                    bgColor = "#1A00B377"
                    borderColor = "#A0E1CC"
                }
            } else {
                tagBean = TagBean().apply {
                    uiStyle = 1
                    text = "商业"
                    textColor = "#FC6B0B"
                    bgColor = "#FFFFFBF8"
                    borderColor = "#FDAC77"
                }
            }
            val listOfTagBean = mutableListOf<TagBean>()
            tagBean?.let { listOfTagBean.add(it) }
            tv_shop_name?.TextWithPrefixTag(listOfTagBean, shopName, 1)
            tv_shop_name?.setOnClickListener {
                mCartAnalysis.cartShopClick(shopCode, shopJumpUrl)
                RoutersUtils.open(shopJumpUrl)
            }

            cbExpand?.isChecked = isExpanded
            cb_shop?.isChecked = selected
            cb_shop?.setOnClickListener {
                goodsAdapter.changeShopSelectStatus(!selected, orgId ?: "", isThirdCompany)
            }
            iv_coupon?.setOnClickListener {
                mCartAnalysis.cartShopCouponClick(it.context,shopCode)
                showShopBottomCartCouponDialog(getCouponCartShopEntryType(),shopCode ?: "", shopName?: "", skuids ?: "", iv_coupon,isTab = true)
            }

            if (showFreightTips) {
                tv_freight?.visibility = View.VISIBLE
                val preFreight = TagBean().apply {
                    uiStyle = 1
                    text = " 运费凑单 "
                    textColor = "#FF0001"
                    bgColor = "#00000000"
                    borderColor = "#FF0001"
                }
                val preFreightTagBeans = mutableListOf<TagBean>()
                preFreightTagBeans.add(preFreight)
                freightTips?.let { tv_freight?.TextWithPrefixTag(preFreightTagBeans, freightTips, 1) }
                if (showFreightIcon) {
                    val drawable = context?.let { ContextCompat.getDrawable(it, R.drawable.icon_hint_image_cart) }
                    drawable?.setBounds(0, 0, ConvertUtils.dp2px(12f), ConvertUtils.dp2px(12f))
                    tv_freight?.setCompoundDrawables(null, null, drawable, null)
                    tv_freight?.setOnClickListener {
                        mCartAnalysis.shopeItemPostClick(shopCode)
                        context?.let { it1 -> FreightTipDialog(it1).showTip(shopCode) }
                    }
                } else {
                    tv_freight?.setCompoundDrawables(null, null, null, null)
                }

                tv_freight_arrow?.visibility = if (freightUrlText.isNullOrEmpty()) View.GONE else View.VISIBLE
                tv_freight_arrow?.text = freightUrlText
                tv_freight_arrow?.setOnClickListener {
                    mCartAnalysis.shopeItemPostToAddClick(shopCode)
                    RoutersUtils.open(freightJumpUrl)
                }

            } else {
                tv_freight?.visibility = View.GONE
                tv_freight_arrow?.visibility = View.GONE
            }

            if (showReturnVoucherInfo) {
                tv_refund?.visibility = View.VISIBLE
                val preRefund = TagBean().apply {
                    uiStyle = 1
                    text = " 返 "
                    textColor = "#F82324"
                    bgColor = "#FFF7F7"
                    borderColor = "#FFC7C8"
                }
                returnVoucherTips?.let { tv_refund?.TextWithPrefixTag(listOf(preRefund), returnVoucherTips, 1) }
                tv_refund_arrow?.visibility = if (returnVoucherUrlText.isNullOrEmpty()) View.GONE else View.VISIBLE
                tv_refund_arrow?.text = returnVoucherUrlText
                tv_refund_arrow?.setOnClickListener {
                    mCartAnalysis.shopeItemReturndClick(shopcode = shopCode, action = returnVoucherJumpUrl)
                    RoutersUtils.open(returnVoucherJumpUrl)
                }
            } else {
                tv_refund?.visibility = View.GONE
                tv_refund_arrow?.visibility = View.GONE
            }

            tv_shop_discounts?.visibility = View.GONE
            tv_shop_discounts_arrow?.visibility = View.GONE
            iv_shop_discounts?.visibility = View.GONE
            shopDiscounts?.let {tipsBean->
                tipsBean.tips?.let {
//                    val preShopDiscounts = TagBean().apply {
//                        uiStyle = 1
//                        text = if (!tipsBean.tagList.isNullOrEmpty()){
//                            tipsBean.tagList[0]?.let {tag->
//                                if (!tag.name.isNullOrEmpty()){
//                                    " ${tag.name} "
//                                }else " 券 "
//                            }?:" 券 "
//                        }else " 券 "
//                        textColor = "#F82324"
//                        bgColor = "#FFF7F7"
//                        borderColor = "#FFC7C8"
//                    }

                    tv_shop_discounts?.visibility = View.VISIBLE
                    iv_shop_discounts?.visibility = View.VISIBLE
//                    tv_shop_discounts?.TextWithPrefixTag(listOf(preShopDiscounts), it, 1)
                    tv_shop_discounts?.text = it
                    try {
                        tv_shop_discounts.setTextColor(Color.parseColor(tipsBean.tipsFontColor?:"#676773"))
                    }catch (e:Exception){
                        tv_shop_discounts.setTextColor(ContextCompat.getColor(requireActivity(),R.color.color_676773))
                    }
                }

                tipsBean.titleUrlText?.let {
                    tv_shop_discounts_arrow?.visibility = View.VISIBLE
                    tv_shop_discounts_arrow?.text = it
                    tv_shop_discounts_arrow?.setOnClickListener {
                        if (tipsBean.haseReceived) {
                            RoutersUtils.open(tipsBean.appUrl)
                            val properties = java.util.HashMap<String, Any>()
                            properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackShoppingCart.TITLE
                            properties[JGTrackManager.FIELD.FIELD_BTN_NAME] = "购物车店铺券凑单"
                            // 移除极光埋点 - btn_click
                            /*
                            eventTrack(requireActivity(), JGTrackManager.TrackSearchResult.EVENT_BTN_CLICK,
                                properties)
                            */
                        } else {
                           mViewModel.getVoucher2(tipsBean.couponTemplateId,tipsBean.appUrl, tipsBean)
                        }
                    }
                }
            }

        }

        cbExpand?.setOnClickListener {
            if (level0ItemShopHeaderBean.isExpanded) {
                val subItemCount = goodsAdapter.collapse(positionCopy, true)
                cbExpand.isSelected = true

                val indexOf = cacheShopHeaderIndex.indexOf(positionCopy)
                val iterate = cacheShopHeaderIndex.listIterator()
                while (iterate.hasNext()) {
                    val oldValue = iterate.next()
                    val index = cacheShopHeaderIndex.indexOf(oldValue)
                    if (index > indexOf) iterate.set(oldValue - subItemCount)
                }
                println("cache position = ${cacheShopHeaderIndex} adapter datasize = ${goodsAdapter.data.size}")
                rv_shop_list?.smoothScrollToPosition(positionCopy)
                goodsAdapter.cachedShopExpandStatus[level0ItemShopHeaderBean.shopCode] = false
                mCartAnalysis.shopeItemCollapseClick(level0ItemShopHeaderBean.shopCode)
            } else {
                val subItemCount = goodsAdapter.expand(positionCopy, true)
                cbExpand.isSelected = false

                val indexOf = cacheShopHeaderIndex.indexOf(positionCopy)
                val iterate = cacheShopHeaderIndex.listIterator()
                while (iterate.hasNext()) {
                    val oldValue = iterate.next()
                    val index = cacheShopHeaderIndex.indexOf(oldValue)
                    if (index > indexOf) iterate.set(oldValue + subItemCount)
                }
                println("cache position = ${cacheShopHeaderIndex} adapter datasize = ${goodsAdapter.data.size}")

                goodsAdapter.cachedShopExpandStatus[level0ItemShopHeaderBean.shopCode] = true
                mCartAnalysis.shopeItemExpandClick(level0ItemShopHeaderBean.shopCode)
            }
            val toJson = Gson().toJson(goodsAdapter.cachedShopExpandStatus)
            MMKV.defaultMMKV().encode("${KEY_SHOP_EXPAND_STATUS}_${SpUtil.getMerchantid()}", toJson)
        }
        iv_coupon?.visibility = if (level0ItemShopHeaderBean.isHaveVoucher) View.VISIBLE else View.GONE
    }

    private fun initReceiver() {
        val intentFilter = IntentFilter()
        intentFilter.addAction(IntentCanst.ACTION_CHANG_CAR_NUMBER)
//        intentFilter.addAction(IntentCanst.ACTION_BUY_PRODUCT)
        LocalBroadcastManager.getInstance(notNullActivity.applicationContext).registerReceiver(mRefreshBroadcastReceiver, intentFilter)
    }

    /*
     * 广播
     * */
    private val mRefreshBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (IntentCanst.ACTION_CHANG_CAR_NUMBER == action) {
//                smart refresh?.autoRefresh()?: kotlin.run {
//                    mViewModel.getCartData(SpUtil.getMerchantid())
//                }
                try {
                    header_layout?.visibility = View.GONE
//                    trackPv()
//                    trackBottomTabExposure()
                    showProgress()
                    mViewModel.getCartData(SpUtil.getMerchantid())
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }


    private fun deleteOrCheckOutShop(cartEntityList: MutableList<MultiItemEntity>?) {
        showProgress()
        val packageIds = mutableListOf<String>()
        val ids = mutableListOf<String>()
        cartEntityList?.forEach {
            if (it is Level0ItemShopHeaderBean) {
                // 编辑删除店铺内所有商品
                it.subItems?.forEach {
                    if (it is Level1ItemGoodsBeanAbs && it.selected) {
                        ids.add(it.skuid ?: "")
                    }
                    if (it is Level1ItemGroupHeaderBean && it.selected) {
                        packageIds.add(it.packageId ?: "")
                    }
                }
            }

            if (it is Level1ItemGoodsBeanAbs) {
                // 删除单个商品
                ids.add(it.skuid ?: "")
            }
            if (it is Level1ItemGroupFooterBean) {
                // 删除组合商品
                packageIds.add(it.packageId ?: "")
            }

            if (it is Level1ItemGroupGoodBean) {
                packageIds.add(it.packageId ?: "")
            }

            if (it is Level1InvalidItemGoodsBeanAbs) {
                // 删除失效品
                ids.add(it.skuid ?: "")
            }
        }


        val packageIdStr = packageIds?.joinToString(",")
        val idStr = ids?.joinToString(",")

        mViewModel.removeProductFromCart(SpUtil.getMerchantid(), packageIdStr?: "", idStr?: "")
    }

    lateinit var popWindowPackage: ShowFreightPopWindow
    private fun setFreightPopwindow() {
        popWindowPackage = context?.let { ShowFreightPopWindow(it) }!!
        popWindowPackage
            .setOutsideTouchable(false)
            .setOnButtonClickListener(object : ShowFreightPopWindow.OnButtonClickListener {
                override fun onRightClick(notSubmitOrderOrgIds: String) {
                    showProgress()
                    mViewModel?.preSettle( if(notSubmitOrderOrgIds.isEmpty())null else notSubmitOrderOrgIds, context)
                    popWindowPackage?.dismiss()
                }
            })
    }

    /**
     * 获取推荐数据
     */
    private fun getRecommendData() {
        val params = getRecommendParams()
//        if (isLoadData) addAnalysisRequestParams(params, mFlowData)
        mViewModel.getRecommendGoodlist(params.paramsMap)
    }

    private var page = 1
    private val limit = 10
    private var isLoadData = false  //是否加载过数据（是否上传过埋点数据）
    private var mRequestMap: Map<String, String>? = null

    private fun getRecommendParams(): RequestParams {
        val params = RequestParams()
        if (mRequestMap != null) {
            mRequestMap?.forEach {
                params.put(it.key, it.value)
            }
            return params
        }
        params.put("pageType", "5") //2 发现页为你推荐 3 首页为你推荐 4 商品详情页为你推荐5 购物车页推荐 6 付款结果页推荐 7 我的-个人中心页内推荐
        params.put("sptype", "3")
        return params
    }

    fun showShopBottomCartCouponDialog(shopCode: String, shopName: String, skuids: String, token: View): Unit {
       showShopBottomCartCouponDialog(null, shopCode, shopName, skuids, token)
    }

    fun showShopBottomCartCouponDialog(couponEntryType: ICouponEntryType?, shopCode: String, shopName: String, skuids: String, token: View,isTab:Boolean = false): Unit {
        val showBottomCartCoupon = ShowBottomCartCouponDialog(isTab,couponEntryType,skuids,shopCode,shopName,VoucherListBean.QUERY_COUPON_TYPE_SHOP,JgTrackBean(
                jgReferrer = <EMAIL>(),
                jgReferrerTitle = JGTrackManager.TrackShoppingCart.TITLE,
                jgReferrerModule = JGTrackManager.TrackShoppingCart.TITLE,
                module = "${JGTrackManager.TrackShoppingCart.TITLE}",
                pageId = JGTrackManager.TrackShoppingCart.PAGE_ID,
                title = JGTrackManager.TrackShoppingCart.TITLE,
                url = <EMAIL>(),
                entrance = JGTrackManager.TrackShoppingCart.TITLE)).apply {
            setOnSelectListener(object : BaseBottomPopWindow.OnSelectListener {
                override fun getValue(show: SearchFilterBean?) {

                }

                override fun OnDismiss() {
                    mViewModel.getCartData(SpUtil.getMerchantid())
                }

            })
        }
        showBottomCartCoupon.show(token)
    }

    var hidden = false
    var isFirst = true


    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        this.hidden = hidden
        println("cartfragment hidden = ${hidden}")
        if (hidden == false) {
            mViewModel.getCartData(SpUtil.getMerchantid())

        }
    }

    override fun onResume() {
        super.onResume()

        if (!hidden && !isFirst) {
            mViewModel.getCartData(SpUtil.getMerchantid())
        }
        isFirst = false

    }

    override fun onVisibleChanged(isVisible: Boolean) {
        super.onVisibleChanged(isVisible)
        if (isVisible) {
            // 初始化资质
            initAptitudeOverdueTip(notNullActivity, layout_aptitude_tip as ConstraintLayout, tv_aptitude_tip, XyyIoUtil.PAGE_SHOPPINGCART)
            val properties: HashMap<String, Any> = HashMap()
            properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackShoppingCart.PAGE_ID
            properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackShoppingCart.TITLE
            properties[JGTrackManager.FIELD.FIELD_URL] = JG_TRACK_URL
            JGTrackManager.pageViewTrack(requireActivity(), JGTrackManager.TrackShoppingCart.TITLE, properties)
        }
    }

    override fun initTitle() {
        setBackTitle(tv_title_name, backTitleAction)
    }

    override fun getParams(): RequestParams? = null

    override fun getUrl() = null

    override fun getLayoutId() = R.layout.fragment_cart_v3

    fun setBackTitle(name: String, action: String) {
        backTitleName = name
        backTitleAction = action
        tv_title_name?.let {
            setBackTitle(tv_title_name, action = backTitleAction)
        }
    }

    inner class RecommendHeaderAdapter(layoutResId: Int, data: MutableList<Int>?) : YBMBaseAdapter<Int>(layoutResId, data) {
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: Int?) {
        }
    }

    override fun getBaseViewModel(): BaseViewModel = mViewModel

    override fun getCouponCartTopEntryType(): ICouponEntryType = CartTopCouponCartEntryType()

    override fun getCouponCartShopEntryType(): ICouponEntryType = CartShopCouponEntryType()

    override fun getCouponCartAddGoodsType(): ICouponEntryType = CartAddGoodCouponEntryType()

}