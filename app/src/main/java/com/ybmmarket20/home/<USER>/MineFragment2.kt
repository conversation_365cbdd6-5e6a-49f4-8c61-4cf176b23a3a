package com.ybmmarket20.home.mine

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.text.TextUtils
import android.view.View
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import butterknife.ButterKnife
import com.google.android.material.appbar.AppBarLayout
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.adapter.FindSameGoodsAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.BoothData
import com.ybmmarket20.bean.MerchantInfo
import com.ybmmarket20.bean.OrderStatusNumber
import com.ybmmarket20.bean.RefreshWrapperPagerBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.ShoppingGoldRechargeBean
import com.ybmmarket20.bean.UseToolsBean
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JGTrackManager.Companion.eventTrack
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.jgTrackResourceProductClick
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.ConstantData
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.db.AccountTable
import com.ybmmarket20.home.MainActivity
import com.ybmmarket20.home.ShopListFragment
import com.ybmmarket20.home.mine.bean.Mine2AmountBean
import com.ybmmarket20.home.mine.bean.Mine2HeaderBean
import com.ybmmarket20.home.mine.bean.Mine2HeaderItemContainer
import com.ybmmarket20.home.mine.bean.Mine2OrderBean
import com.ybmmarket20.message.Message
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.AdapterUtils.addLocalTimeForRows
import com.ybmmarket20.utils.AdapterUtils.getAfterDiscountPrice
import com.ybmmarket20.utils.PushUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.StatusBarUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.FlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.flowDataPageCommoditySearch
import com.ybmmarket20.viewmodel.AccountInfoViewModel
import com.ybmmarket20.viewmodel.Mine2ViewModel
import com.ybmmarket20.viewmodel.SearchDataViewModel
import com.ybmmarket20.xyyreport.page.mine.MineReport
import com.ybmmarket20.xyyreport.page.search.SearchReportConstant.EXTENSION_SEARCH_GOODS_SCM_ID
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarketkotlin.bean.ApplyNoticeBean
import com.ybmmarketkotlin.viewmodel.PayNoticeViewModel
import com.ybmmarketkotlin.views.createViewList
import kotlinx.android.synthetic.main.fragment_mine2.abl_mine2
import kotlinx.android.synthetic.main.fragment_mine2.crv_recommend
import kotlinx.android.synthetic.main.fragment_mine2.iv_mine2_message_title
import kotlinx.android.synthetic.main.fragment_mine2.iv_mine2_setting_title
import kotlinx.android.synthetic.main.fragment_mine2.ll_title
import kotlinx.android.synthetic.main.fragment_mine2.title_tv
import kotlinx.android.synthetic.main.fragment_mine2.tv_mine2_message_bubble_title
import kotlinx.android.synthetic.main.header_mine2.hsbv_minebanner
import kotlinx.android.synthetic.main.header_mine2.hssv_minestreamer
import kotlinx.android.synthetic.main.header_mine2.ll_marqueue_pay_notice
import kotlinx.android.synthetic.main.header_mine2.marquee_pay_notice
import kotlinx.android.synthetic.main.header_mine2.mine2_amount
import kotlinx.android.synthetic.main.header_mine2.mine2_common_tools
import kotlinx.android.synthetic.main.header_mine2.mine2_header
import kotlinx.android.synthetic.main.header_mine2.mine2_order
import kotlinx.android.synthetic.main.view_mine2_order_v2.view.tvMyOrderEntry


/**
 * 我的
 */
class MineFragment2: MineFragmentAnalysisV2(), View.OnClickListener {
    private var isLoadData = false//是否加载过数据（是否上传过埋点数据）
    private var recommendList: MutableList<RowsBean> = ArrayList()
    private var recommendAdapter: FindSameGoodsAdapter? = null
    private var mParamsMap: HashMap<String, String> = hashMapOf("sptype" to "3", "pageType" to "7", "merchantId" to SpUtil.getMerchantid())
    private val mViewModel: SearchDataViewModel by viewModels()
    private val mine2ViewModel: Mine2ViewModel by viewModels()
    private val payNoticeViewModel: PayNoticeViewModel by viewModels()
    private val accountInfoViewModel: AccountInfoViewModel by viewModels()
    private val headerMessageListener by lazy { Message.Listener(mine2_header::setMessageCount) }
    private val titleMessageListener by lazy { Message.Listener{ count -> Message.showMsgCount2(count, tv_mine2_message_bubble_title) } }
    private var isFirst = false
    private var pingAnBlock: ((Int)->Unit)? = null

    private val mInfoAndMsgReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (TextUtils.isEmpty(action)) {
                return
            }
            if (IntentCanst.ACTION_MERCHANTBASEINFO == action) {
                initData(null)
            } else if (IntentCanst.ACTION_ORDER_STATUS == action) {
                getOderStatus()
            } else if (IntentCanst.ACTION_SWITCH_USER == action) {
                refreshUserInfo()
                clearRecommendData()
            } else if (IntentCanst.ACTION_CONFIRM_REFUND_STATUS == action) {
                val orderId = intent.getStringExtra("orderId")
                val status = intent.getIntExtra("status", -1)
                marqueeRemoveConfirmOrder(orderId?: "", status)
            }
        }
    }
    /**
     * fragment是否显示
     */
    private var isShow: Boolean = true

    companion object {

        fun jgTrackBtnClick(mContext: Context,module:String,btnName:String){
            val params = java.util.HashMap<String, Any>()
            params[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackMine.PAGE_ID
            params[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackMine.TITLE
            params[JGTrackManager.FIELD.FIELD_REFERRER] = ShopListFragment.JG_TRACK_URL
            params[JGTrackManager.FIELD.FIELD_URL] = this.getFullClassName()
            params[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = this.getFullClassName()
            params[JGTrackManager.FIELD.FIELD_MODULE] = module
            params[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName
            // 移除极光埋点 - btn_click
            // eventTrack(mContext, JGTrackManager.TrackMine.EVENT_BTN_CLICK, params)
        }
    }

    override fun getLayoutId(): Int = R.layout.fragment_mine2

    /**
     * 初始化
     */
    override fun initData(content: String?) {
        recommendAdapter = FindSameGoodsAdapter(recommendList, requireContext(),jgTrackBean = JgTrackBean(
                jgReferrer = this.getFullClassName(),
                jgReferrerTitle = JGTrackManager.TrackMine.TITLE,
                jgReferrerModule = JGTrackManager.TrackMine.TITLE,
                module = JGTrackManager.Common.MODULE_PRODUCT_LIST,
                pageId = JGTrackManager.TrackMine.PAGE_ID,
                title = JGTrackManager.TrackMine.TITLE,
                entrance = JGTrackManager.TrackMine.TITLE+"(热销精选)",
                url = this.getFullClassName(),
        )).apply {
            resourceViewTrackListener = { rowsBean, i ->
                var productTag = ""
                rowsBean.tags?.productTags?.let { tagList ->
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
                rowsBean.tags?.dataTags?.let { tagList ->
                    if (productTag.isNotEmpty()) {
                        productTag += ","
                    }
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }

            }

            productClickTrackListener = { rowsBean, i,number ->
                var productTag = ""
                rowsBean.tags?.productTags?.let { tagList ->
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
                rowsBean.tags?.dataTags?.let { tagList ->
                    if (productTag.isNotEmpty()) {
                        productTag += ","
                    }
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
                requireActivity().jgTrackResourceProductClick(
                        url = <EMAIL>(),
                        module = JGTrackManager.Common.MODULE_PRODUCT_LIST,
                        referrer = <EMAIL>(),
                        pageId = JGTrackManager.TrackMine.PAGE_ID,
                        title = JGTrackManager.TrackMine.TITLE,
                        resourceId = "",
                        resourceName = "",
                        resourceType = "",
                        position = i,
                        productId = rowsBean.productId ?:"",
                        productName = rowsBean.productName?:"",
                        productType = "普通商品",
                        productPrice = rowsBean.jgProductPrice ?: 0.0,
                        productLabel = productTag,
                        entrance = jgTrackBean?.entrance?:"",
                        navigation = ""
                )
            }
        }
        isFirst = true
        //初始化广播接收器
        initReceiver()
        //初始化观察者
        initObserver()
        //监听消息变化
        setMessageListener()
        //设置列表滚动监听
        setScrollListener()
        //获取为支付订单(立即支付)
        getUnPayOrders()
        //获取推荐商品流
        getRecommendGoods()
        //初始化点击监听
        initClickListener()
        //推荐列表加载
        recommendListLoad()
    }

    override fun onResume() {
        super.onResume()
        //activity回到此面需要刷新状态
        refreshUserInfo()
        isFirst = false
        if (isShow){
            jgTrackPageView()
        }
    }

    // 刷新用户信息
    private fun refreshUserInfo() {
        getMerchantBaseInfo(isFirst)
        getUseTools()
        Message.instance.reInit()
        getBoothData()
        getUnPayOrders()
        accountInfoViewModel.getAccountId()
        //购物金
        mine2ViewModel.getShoppingGoldRechargeBean()
    }

    private fun clearRecommendData() {
        mParamsMap.clear()
        mParamsMap = hashMapOf("sptype" to "3", "pageType" to "7", "merchantId" to SpUtil.getMerchantid())
        recommendList.clear()
        recommendAdapter = FindSameGoodsAdapter(recommendList, requireContext())
        recommendListLoad()
        getRecommendGoods()
    }

    /**
     * 推荐列表加载更多
     */
    private fun recommendListLoad() {
        crv_recommend.layoutManager = StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL)
//        crv_recommend.addItemDecoration(FindSameGoodsActivity.FindSameGoodsItemDecoration(context))
        crv_recommend.adapter = recommendAdapter
        recommendAdapter?.setEnableLoadMore(true)
        recommendAdapter?.setOnLoadMoreListener {
            getRecommendGoods()
        }
    }

    /**
     * 设置消息监听
     */
    private fun setMessageListener() {
        Message.instance.bindUnreadMsgCount(headerMessageListener)
        Message.instance.bindUnreadMsgCount(titleMessageListener)
    }

    /**
     * 初始化点击监听
     */
    private fun initClickListener() {
        iv_mine2_message_title.setOnClickListener(this)
        iv_mine2_setting_title.setOnClickListener(this)
        mine2_order.tvMyOrderEntry.setOnClickListener(this)
        mine2_order.mSkipOrderListPage = { state ->
            (notNullActivity as MainActivity).showOrderShop(state)
        }

        mine2ViewModel.shoppingGoldRechargeBeanData.observe(requireActivity()){
            if (it.isSuccess){
                setShoppingGoldData(it.data)
            }
        }
    }

    /**
     * 处理平安蒙层
     */
    private fun handlePingAnMask(isShowFlag: Int) {
        pingAnBlock?.invoke(isShowFlag)
    }

    fun setOnPingAnClickListener(block: ((Int)->Unit)?) {
        pingAnBlock = block
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.iv_mine2_setting_title -> {

                    RoutersUtils.open("ybmpage://elsepage")
                    XyyIoUtil.track("action_Me_Install")
//                val intent = Intent(context, CommonH5Activity::class.java).apply {
//                    putExtra("htmlStr", "123")
//                    putExtra("url", "ybmpage://space")
//                }
//                startActivity(intent)
            }
            R.id.iv_mine2_message_title -> {
                Message.openMessagePage()
                XyyIoUtil.track("action_Me_Message")

            }
            R.id.tvMyOrderEntry -> {
                context?.let(MineReport::clickSubModuleOrderAll)
                (notNullActivity as MainActivity).showOrderShop("0")
                XyyIoUtil.track("action_Me_AllOrders")
            }
            else -> {}
        }
    }

    //title的高度
    private var titleHeight = 0
    //前置偏移量
    private val preposition = 120
    private val level = 3.0f
    /**
     * 设置列表滚动监听
     */
    private fun setScrollListener() {
        abl_mine2.addOnOffsetChangedListener(AppBarLayout.OnOffsetChangedListener { _, verticalOffset ->
            if (titleHeight == 0) {
                titleHeight = ll_title.height
            }
            var alpha = (-verticalOffset - preposition) * level / titleHeight
            if (alpha >= 0) {
                ll_title.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.white))
            } else {
                ll_title.background = null
                alpha = 0f
            }
            title_tv.alpha = alpha
            iv_mine2_setting_title.alpha = alpha
            iv_mine2_message_title.alpha = alpha
            tv_mine2_message_bubble_title.alpha = alpha
        })
    }

    override fun initTitle() {
        // 为异形屏幕增加状态栏留白
        val layoutParams = ll_title!!.layoutParams
        layoutParams.height += StatusBarUtils.getStatusBarHeight(notNullActivity)
        ll_title!!.layoutParams = layoutParams
    }
//
//    fun setAlertDialogEx(phone: String, onClickListener: AlertDialogEx.OnClickListener?) {
//        val dialogEx = AlertDialogEx(BaseYBMApp.getApp().currActivity)
//        dialogEx.setMessage("呼叫专属销售：$phone").setCancelButton("取消", null)
//            .setConfirmButton("呼叫", onClickListener).show()
//    }

    override fun onDestroyView() {
        super.onDestroyView()
        ButterKnife.unbind(this)
        LocalBroadcastManager.getInstance(notNullActivity.applicationContext)
            .unregisterReceiver(mInfoAndMsgReceiver)
        Message.instance.releaseListener(headerMessageListener)
        Message.instance.releaseListener(titleMessageListener)
    }

    /**
     * 设置商户基本信息
     */
    private fun setBaseInfo(baseInfo: MerchantInfo.BaseInfo) {
        try {
            val mine2HeaderItemContainer = Mine2HeaderItemContainer("Mine2Header", Mine2HeaderBean(baseInfo.realName))
            mine2_header.setData(mine2HeaderItemContainer)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 设置资产
     */
    private fun setAmount(accountInfo: MerchantInfo.AccountInfo) {

        val amountList = mutableListOf<Mine2AmountBean>()
        val virtualMoney =
            if (TextUtils.isEmpty(accountInfo.virtualGold)) "0.00" else UiUtils.transform(
                accountInfo.virtualGold
            )
        val virtualMoneyAmountBean = Mine2AmountBean(
            MINE2_AMOUNT_VIRTUAL_MONEY,
            virtualMoney,
            des = "购物金",
            mine2AmountItemType = MINE2_AMOUNT_ITEM_TYPE_WITH_NEW
        )
        amountList.add(virtualMoneyAmountBean)

        val coupon = if (accountInfo.voucherCount == 0) "0" else accountInfo.voucherCount.toString() + ""
        val couponAmountBean = Mine2AmountBean(MINE2_AMOUNT_COUPON, coupon, "优惠券", mine2AmountItemType = MINE2_AMOUNT_ITEM_TYPE_WITH_NEW)
        amountList.add(couponAmountBean)

        val redEnvelopGold = if (TextUtils.isEmpty(accountInfo.redPacketBalance)) "0" else UiUtils.transform(accountInfo.redPacketBalance)
        val redEnvelopeAmountBean = Mine2AmountBean(MINE2_AMOUNT_RED_ENVELOPE, redEnvelopGold, "红包",  mine2AmountItemType = MINE2_AMOUNT_ITEM_TYPE_WITH_NEW)
        amountList.add(redEnvelopeAmountBean)

        val myRichAmountBean = Mine2AmountBean(MINE2_AMOUNT_MY_RICH,"查看", des = "我的财富", mine2AmountItemType = MINE2_AMOUNT_ITEM_TYPE_WITH_NEW)
        amountList.add(myRichAmountBean)

//        amountList.add(redEnvelopeAmountBean)
//        amountList.add(couponAmountBean)
//        if (accountInfo.pingAnCredShowFlag == 1) {
//            val pingAnGold = if (TextUtils.isEmpty(accountInfo.pingAnCredBalance)) "0" else UiUtils.transform(accountInfo.pingAnCredBalance)
//            val pingAnBean = Mine2AmountBean(MINE2_AMOUNT_PING_AN, pingAnGold, "平安贷", R.drawable.icon_ping_an)
//            amountList.add(pingAnBean)
//        }
//        ivPingAnTag.visibility = if (accountInfo.pingAnCredShowFlag == 1) View.VISIBLE else View.GONE

        val container = Mine2HeaderItemContainer("Mine2Amount", amountList)
        try {
            mine2_amount.isShowDivider = false
            mine2_amount.setData(container)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun setShoppingGoldData(mBean: ShoppingGoldRechargeBean?) {
        mine2_amount.setShoppingGoldData(mBean)
    }

    /**
     * 设置订单
     */
    private fun setOrder() {
        //数据固定，只设置一次
        if (mine2_order.mContainer != null) return
        val prePayOrderBean = Mine2OrderBean(MINE_ORDER_TYPE_DZF, R.drawable.icon_wait_pay, 0, "待支付", null, "action_Me_NoPaid", 0)
        val preDeliverOrderBean = Mine2OrderBean(MINE_ORDER_TYPE_DPS, R.drawable.icon_wait_deliver, 0, "待配送", null, "action_Me_NoDistribution", 1)
        val deliverProcessOrderBean = Mine2OrderBean(MINE_ORDER_TYPE_PSZ, R.drawable.icon_wait_receive, 0, "配送中", null, "action_Me_InDistribution", 2)
        val preEvaluateOrderBean = Mine2OrderBean(MINE_ORDER_TYPE_DPJ, R.drawable.icon_wait_balance, 0, "待评价", null, "action_Me_Evaluation", 4)
        val refundOrderBean = Mine2OrderBean(MINE_ORDER_TYPE_TKORSH, R.drawable.icon_wait_service, 0, "退款/售后", null, "action_Me_RefundAfterSale", 3)
        val orderButtonList = mutableListOf(prePayOrderBean, preDeliverOrderBean, deliverProcessOrderBean, refundOrderBean, preEvaluateOrderBean)
        mine2_order.setData(Mine2HeaderItemContainer("Mine2Order", orderButtonList))
        mine2_order.setOnOrderTypeItemClickAnalysisCallback(::onOrderTypeItemAnalysis)
    }

    /**
     * 我的-商户基本信息
     */
    private fun getMerchantBaseInfo(show: Boolean) {
        if (show) {
            showProgress()
        }
        val merchantId = SpUtil.getMerchantid()
        val params = RequestParams()
        params.put("merchantId", merchantId)
        HttpManager.getInstance().post(AppNetConfig.BASE_INFO, params, object: BaseResponse<MerchantInfo?>() {
            override fun onSuccess(
                content: String?,
                obj: BaseBean<MerchantInfo?>?,
                data: MerchantInfo?
            ) {
                super.onSuccess(content, obj, data)
                if (show) {
                    dismissProgress()
                }
                if (obj != null && obj.isSuccess && data != null) {
                    // 是否是ka用户
                    try {
                        SpUtil.setValidityStatus(data.validity)
                        val account = data.accountInfo
                        val baseInfo = data.baseInfo
                        if (account == null || baseInfo == null) return
                        //设置商户今本信息
                        setBaseInfo(baseInfo)
                        //设置资产
                        setAmount(account)
                        //设置订单按钮
                        setOrder()
                        getOderStatus()
                        //小药白条心愿单传递参数赋值
                        //请求完数据更新本地的信息
                        AccountTable.update(
                            notNullActivity,
                            merchantId,
                            baseInfo.realName,
                            SpUtil.getLoginPhone(),
                            baseInfo.address
                        )
                        //平安弹窗
                        handlePingAnMask(data.accountInfo.pingAnCredShowFlag)
                        //region  诸葛重新绑定账户
                        SpUtil.writeString(ConstantData.PROVINCECODE, baseInfo.provinceCode)
                        SpUtil.writeString(ConstantData.PROVINCE, baseInfo.province)
                        XyyIoUtil.identify(merchantId, baseInfo)
                        //endregion
                        if (data.tagList != null && data.tagList.isNotEmpty()) {
                            //更新推送tags
                            PushUtil.setTags(data.tagList)
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                dismissProgress()
            }
        })
    }

    private fun initReceiver() {
        val intentFilter = IntentFilter()
        intentFilter.addAction(IntentCanst.ACTION_MERCHANTBASEINFO)
        intentFilter.addAction(IntentCanst.ACTION_ORDER_STATUS)
        intentFilter.addAction(IntentCanst.ACTION_SWITCH_USER)
        intentFilter.addAction(IntentCanst.ACTION_CONFIRM_REFUND_STATUS)
        LocalBroadcastManager.getInstance(notNullActivity.applicationContext).registerReceiver(
            mInfoAndMsgReceiver, intentFilter
        )
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        isShow = !hidden
        if (!hidden) {
            refreshUserInfo()
            jgTrackPageView()
        }
    }


    /**
     * 获取推荐商品流
     */
    private fun getRecommendGoods() {
        crv_recommend.post {
            if (mParamsMap["merchantId"] != SpUtil.getMerchantid()) {
                mParamsMap.clear()
                mParamsMap = hashMapOf("sptype" to "3", "pageType" to "7", "merchantId" to SpUtil.getMerchantid())
                recommendList.clear()
                recommendAdapter?.notifyDataSetChanged()
            }
            mViewModel.getRecommendSearchData(mParamsMap)
        }
    }

    /**
     * 获取常用工具
     */
    private fun getUseTools() {
        mine2ViewModel.getCommonTools()
    }

    /**
     * 获取待支付订单，提醒支付
     */
    private fun getUnPayOrders() {
        payNoticeViewModel.getPayNotice(SpUtil.getMerchantid())
    }

    private fun marqueeRemoveConfirmOrder(orderId: String, status: Int) {
        payNoticeViewModel.removeOrder(orderId, status)
    }

    /**
     * 获取订单气泡数量
     */
    private fun getOderStatus() {
        mine2ViewModel.getOrdersBubbleCount()
    }

    /**
     * 获取个人中心展位数据
     */
    private fun getBoothData() {
        mine2ViewModel.getBoothData("3")
    }

    /**
     * 添加观察者
     */
    private fun initObserver() {
        //常用工具
        mine2ViewModel.commonToolsLiveData.observe(this, CommonToolsObserver())
        //推荐商品流
        mViewModel.recommendLiveData.observe(this, RecommendObserver())
        //获取待支付订单
        payNoticeViewModel.payNoticeBeanLiveData.observe(this, UnPayOrdersObserver())
        //订单气泡数量
        mine2ViewModel.ordersBubbleCountData.observe(this, OrdersBubbleCountObserver())
        //展位
        mine2ViewModel.boothLiveData.observe(this, BoothDataObserver())
    }

    /**
     * 常用工具Observer
     */
    inner class CommonToolsObserver: Observer<BaseBean<List<UseToolsBean>>> {
        @SuppressLint("NotifyDataSetChanged")
        override fun onChanged(t: BaseBean<List<UseToolsBean>>) {
            if (!t.isSuccess) return
            val container = Mine2HeaderItemContainer("CommonTools", t.data)
            mine2_common_tools.setData(container)
        }
    }

    /**
     * 推荐商品流Observer
     */
    inner class RecommendObserver: Observer<BaseBean<RefreshWrapperPagerBean<RowsBean>>> {
        @SuppressLint("NotifyDataSetChanged")
        override fun onChanged(t: BaseBean<RefreshWrapperPagerBean<RowsBean>>) {
            if (!t.isSuccess) return
            val pagerBean = t.data
            addLocalTimeForRows(pagerBean.rows)
            initTrackData(pagerBean)
            if (pagerBean?.rows == null) {
                // 兜底，防止崩溃
                return
            }
            mParamsMap = HashMap(pagerBean.requestParam?: mapOf())
            recommendList.addAll(pagerBean.rows)
            // 请求并更新折后价
            getAfterDiscountPrice(
                pagerBean.rows,
                recommendAdapter!!,
                true
            )
            recommendAdapter?.notifyDataChangedAfterLoadMore(!pagerBean.isEnd)
            if (!isLoadData) {
                mFlowData.sId = pagerBean.sid
                mFlowData.spId = pagerBean.spId
                mFlowData.spType = pagerBean.spType
                isLoadData = true
                flowDataPageCommoditySearch(mFlowData)
                val flowData = FlowData(mFlowData.spType, mFlowData.spId, mFlowData.sId, mFlowData.nsid, mFlowData.sdata, null)
                recommendAdapter?.setFindSameFlowData(flowData)
            }
        }

    }

    /**
     * 待支付订单提示
     */
    inner class UnPayOrdersObserver: Observer<BaseBean<List<ApplyNoticeBean>>> {
        override fun onChanged(t: BaseBean<List<ApplyNoticeBean>>) {
            val applyNoticeBeans = t.data
            if (applyNoticeBeans.isNullOrEmpty()) {
                ll_marqueue_pay_notice!!.visibility = View.GONE
            } else {
                ll_marqueue_pay_notice!!.visibility = View.VISIBLE
                val viewList =
                    marquee_pay_notice!!.createViewList(applyNoticeBeans.toMutableList())
                marquee_pay_notice!!.startWithViewList(viewList, 1)
            }
            dismissProgress()
        }

    }

    /**
     * 获取订单气泡数量
     */
    inner class OrdersBubbleCountObserver: Observer<BaseBean<OrderStatusNumber>> {
        override fun onChanged(t: BaseBean<OrderStatusNumber>) {
            if (t.isSuccess) {
                val data = t.data
                mine2_order.setOrderBubbleCount(listOf(
                    data.waitPayNum,
                    data.waitShippingNum,
                    data.waitReceiveNum,
                    data.refundNum,
                    data.waitAppraiseNum,
                ))
            }
        }
    }

    /**
     * 个人中心展位
     */
    inner class BoothDataObserver: Observer<BaseBean<BoothData>> {
        override fun onChanged(t: BaseBean<BoothData>) {
            if (!t.isSuccess) return
            val boothData = t.data
            if (boothData?.detail == null) return
            val boothDetail = boothData.detail
            if (boothDetail.bannerDto == null && boothDetail.steamerDto == null) {
                hsbv_minebanner.visibility = View.GONE
                hssv_minestreamer.visibility = View.GONE
                return
            }
            if (boothDetail.type == 1) {
                //轮播类型
                hssv_minestreamer.visibility = View.GONE
                hsbv_minebanner.visibility = View.VISIBLE
                if (boothDetail.bannerDto == null) return
                hsbv_minebanner.setData(boothDetail.bannerDto.imageDtos)
                hsbv_minebanner.setBannerAnalysisCallback { _, index ->
                    XyyIoUtil.track("action_Me_Banner Ad_Click", hashMapOf("offset" to "$index"))
                }
                XyyIoUtil.track("action_Me_Banner Ad_Popup")
            } else if (boothDetail.type == 2) {
                //胶囊类型
                boothDetail.steamerDto.isHotZone = true
                hsbv_minebanner.visibility = View.GONE
                hssv_minestreamer.visibility = View.VISIBLE
                if (boothDetail.steamerDto == null) return
                hssv_minestreamer.setStreamer(boothDetail.steamerDto)
            }
        }
    }

    override fun getParams(): RequestParams {
        return RequestParams()
    }

    override fun getUrl(): String? {
        return null
    }

    private fun jgTrackPageView(){
        val properties: HashMap<String, Any> = HashMap()
        properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackMine.PAGE_ID
        properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackMine.TITLE
        properties[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackMine.TRACK_URL
        JGTrackManager.pageViewTrack(requireActivity(), JGTrackManager.TrackMine.TITLE,properties)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun handlePv() {
        super.handlePv()
        if (recommendList.isNotEmpty()) {
            recommendAdapter?.onClear()
            val rowsBean = recommendList[0]
            rowsBean.reloadTag ++
            recommendAdapter?.notifyDataSetChanged()
        }
    }
}